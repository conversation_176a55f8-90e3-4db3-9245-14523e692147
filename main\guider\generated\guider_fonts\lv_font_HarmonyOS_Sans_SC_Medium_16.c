/*******************************************************************************
 * Size: 16 px
 * Bpp: 4
 * Opts: undefined
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef LV_FONT_HARMONYOS_SANS_SC_MEDIUM_16
#define LV_FONT_HARMONYOS_SANS_SC_MEDIUM_16 1
#endif

#if LV_FONT_HARMONYOS_SANS_SC_MEDIUM_16

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0xe, 0xc0, 0xd, 0xc0, 0xd, 0xb0, 0xd, 0xb0,
    0xc, 0xa0, 0xc, 0xa0, 0xb, 0xa0, 0xb, 0x90,
    0xb, 0x90, 0x2, 0x10, 0x1, 0x10, 0xf, 0xe0,
    0xd, 0xb0,

    /* U+0022 "\"" */
    0x17, 0x22, 0x71, 0x1f, 0x54, 0xf2, 0x1f, 0x44,
    0xf1, 0x1f, 0x44, 0xf1, 0xf, 0x43, 0xf1,

    /* U+0023 "#" */
    0x0, 0x3, 0xf5, 0x4, 0xf3, 0x0, 0x0, 0x7f,
    0x10, 0x8f, 0x0, 0x6, 0x9d, 0xf9, 0x9e, 0xe9,
    0x30, 0xbf, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x2f,
    0x50, 0x3f, 0x40, 0x0, 0x6, 0xf2, 0x7, 0xf0,
    0x0, 0x0, 0x9e, 0x0, 0xad, 0x0, 0x7, 0x9e,
    0xe9, 0x9e, 0xd9, 0x20, 0xcf, 0xff, 0xff, 0xff,
    0xf4, 0x0, 0x4f, 0x40, 0x5f, 0x20, 0x0, 0x7,
    0xf0, 0x8, 0xf0, 0x0, 0x0, 0xbd, 0x0, 0xcb,
    0x0, 0x0,

    /* U+0024 "$" */
    0x0, 0x0, 0xf8, 0x0, 0x0, 0x0, 0xf, 0x80,
    0x0, 0x0, 0x5b, 0xfe, 0x81, 0x0, 0x7f, 0xef,
    0xff, 0xd0, 0xf, 0xc0, 0xf8, 0x7f, 0x72, 0xf7,
    0xf, 0x80, 0x70, 0xf, 0xc1, 0xf8, 0x0, 0x0,
    0x8f, 0xef, 0x80, 0x0, 0x0, 0x5d, 0xff, 0xc3,
    0x0, 0x0, 0xf, 0xdf, 0xf3, 0x1, 0x0, 0xf8,
    0x3f, 0xa5, 0xf1, 0xf, 0x80, 0xeb, 0x3f, 0xc2,
    0xf8, 0x5f, 0x80, 0x7f, 0xff, 0xff, 0xd1, 0x0,
    0x39, 0xfd, 0x70, 0x0, 0x0, 0xf, 0x80, 0x0,
    0x0, 0x0, 0xf8, 0x0, 0x0,

    /* U+0025 "%" */
    0x8, 0xee, 0x80, 0x0, 0x4f, 0x70, 0x6, 0xf8,
    0x9f, 0x50, 0xd, 0xd0, 0x0, 0xad, 0x0, 0xd9,
    0x6, 0xf4, 0x0, 0x8, 0xf4, 0x4f, 0x71, 0xeb,
    0x0, 0x0, 0x1d, 0xff, 0xc1, 0x9f, 0x20, 0x0,
    0x0, 0x3, 0x30, 0x3f, 0x80, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xe0, 0x1, 0x0, 0x0, 0x0, 0x5,
    0xf6, 0x1c, 0xff, 0x80, 0x0, 0x0, 0xec, 0xa,
    0xe6, 0x9f, 0x40, 0x0, 0x8f, 0x30, 0xd9, 0x0,
    0xf7, 0x0, 0x2f, 0xa0, 0xa, 0xe5, 0x8f, 0x30,
    0xa, 0xf1, 0x0, 0xa, 0xfe, 0x60,

    /* U+0026 "&" */
    0x0, 0x7, 0xdf, 0xc4, 0x0, 0x0, 0x0, 0x7f,
    0xd9, 0xef, 0x20, 0x0, 0x0, 0xcf, 0x0, 0x6f,
    0x60, 0x0, 0x0, 0xcf, 0x0, 0x7f, 0x50, 0x0,
    0x0, 0x6f, 0x86, 0xfc, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x90, 0x5, 0x30, 0x1, 0xcf, 0xff, 0x30,
    0x3f, 0x60, 0xc, 0xf5, 0x2e, 0xe2, 0x6f, 0x30,
    0x2f, 0xa0, 0x4, 0xfd, 0xce, 0x0, 0x4f, 0x70,
    0x0, 0x6f, 0xf7, 0x0, 0x2f, 0xb0, 0x0, 0x1e,
    0xfa, 0x0, 0xa, 0xfb, 0x78, 0xee, 0xbf, 0x70,
    0x0, 0x8d, 0xfe, 0x91, 0xc, 0xf5,

    /* U+0027 "'" */
    0x17, 0x21, 0xf5, 0x1f, 0x41, 0xf4, 0xf, 0x40,

    /* U+0028 "(" */
    0x0, 0x4a, 0x20, 0x1e, 0xa0, 0x9, 0xf1, 0x1,
    0xf9, 0x0, 0x6f, 0x40, 0xa, 0xf0, 0x0, 0xde,
    0x0, 0xe, 0xc0, 0x0, 0xec, 0x0, 0xd, 0xd0,
    0x0, 0xbf, 0x0, 0x7, 0xf3, 0x0, 0x3f, 0x70,
    0x0, 0xce, 0x0, 0x4, 0xf6, 0x0, 0x8, 0xf2,

    /* U+0029 ")" */
    0x5a, 0x10, 0x0, 0xeb, 0x0, 0x6, 0xf4, 0x0,
    0xe, 0xc0, 0x0, 0x9f, 0x10, 0x5, 0xf5, 0x0,
    0x2f, 0x80, 0x1, 0xf9, 0x0, 0x1f, 0xa0, 0x2,
    0xf8, 0x0, 0x4f, 0x70, 0x8, 0xf3, 0x0, 0xce,
    0x0, 0x3f, 0x70, 0xb, 0xe0, 0x6, 0xf3, 0x0,

    /* U+002A "*" */
    0x0, 0x1f, 0x20, 0x1, 0xb2, 0xf2, 0xb2, 0x2b,
    0xef, 0xec, 0x20, 0x3d, 0xfe, 0x30, 0x4f, 0x9f,
    0x9f, 0x40, 0x31, 0xf1, 0x30, 0x0, 0x18, 0x10,
    0x0,

    /* U+002B "+" */
    0x0, 0x0, 0x21, 0x0, 0x0, 0x0, 0x1f, 0x60,
    0x0, 0x0, 0x1, 0xf6, 0x0, 0x0, 0x0, 0x1f,
    0x60, 0x0, 0x3f, 0xff, 0xff, 0xff, 0x81, 0x88,
    0x9f, 0xb8, 0x84, 0x0, 0x1, 0xf6, 0x0, 0x0,
    0x0, 0x1f, 0x60, 0x0, 0x0, 0x1, 0xf6, 0x0,
    0x0,

    /* U+002C "," */
    0x1, 0x20, 0xd, 0xf1, 0xb, 0xf3, 0x0, 0xe1,
    0xb, 0x90, 0x5, 0x0,

    /* U+002D "-" */
    0x2, 0x22, 0x22, 0x10, 0xff, 0xff, 0xfd, 0x5,
    0x55, 0x55, 0x40,

    /* U+002E "." */
    0x2, 0x10, 0xfd, 0xd, 0xb0,

    /* U+002F "/" */
    0x0, 0x0, 0x9f, 0x10, 0x0, 0xe, 0xa0, 0x0,
    0x4, 0xf4, 0x0, 0x0, 0xae, 0x0, 0x0, 0x1f,
    0x90, 0x0, 0x6, 0xf3, 0x0, 0x0, 0xcd, 0x0,
    0x0, 0x2f, 0x70, 0x0, 0x8, 0xf2, 0x0, 0x0,
    0xdc, 0x0, 0x0, 0x3f, 0x60, 0x0, 0x9, 0xf0,
    0x0, 0x0,

    /* U+0030 "0" */
    0x0, 0x4c, 0xfe, 0x70, 0x0, 0x3f, 0xfb, 0xef,
    0x70, 0xb, 0xf3, 0x0, 0xef, 0x0, 0xfd, 0x0,
    0x8, 0xf4, 0x2f, 0xa0, 0x0, 0x5f, 0x73, 0xf9,
    0x0, 0x4, 0xf8, 0x3f, 0x90, 0x0, 0x4f, 0x82,
    0xfa, 0x0, 0x5, 0xf7, 0xf, 0xd0, 0x0, 0x8f,
    0x40, 0xbf, 0x30, 0xe, 0xf0, 0x3, 0xff, 0xbe,
    0xf7, 0x0, 0x4, 0xcf, 0xe7, 0x0,

    /* U+0031 "1" */
    0x0, 0x2b, 0xf5, 0x8, 0xff, 0xf5, 0x7f, 0xa8,
    0xf5, 0x53, 0x6, 0xf5, 0x0, 0x6, 0xf5, 0x0,
    0x6, 0xf5, 0x0, 0x6, 0xf5, 0x0, 0x6, 0xf5,
    0x0, 0x6, 0xf5, 0x0, 0x6, 0xf5, 0x0, 0x6,
    0xf5, 0x0, 0x6, 0xf5,

    /* U+0032 "2" */
    0x0, 0x5c, 0xfe, 0x70, 0x0, 0x6f, 0xeb, 0xef,
    0x80, 0xf, 0xc0, 0x1, 0xff, 0x0, 0x43, 0x0,
    0xc, 0xf1, 0x0, 0x0, 0x0, 0xee, 0x0, 0x0,
    0x0, 0x7f, 0x80, 0x0, 0x0, 0x5f, 0xc0, 0x0,
    0x0, 0x3f, 0xe1, 0x0, 0x0, 0x2e, 0xe2, 0x0,
    0x0, 0x1e, 0xf3, 0x0, 0x0, 0xd, 0xfd, 0x99,
    0x99, 0x43, 0xff, 0xff, 0xff, 0xf7,

    /* U+0033 "3" */
    0x0, 0x7d, 0xfe, 0x80, 0x0, 0x9f, 0xda, 0xef,
    0xa0, 0xc, 0x90, 0x0, 0xef, 0x0, 0x0, 0x0,
    0x1f, 0xe0, 0x0, 0x6, 0xae, 0xe4, 0x0, 0x0,
    0xaf, 0xfd, 0x30, 0x0, 0x0, 0x15, 0xff, 0x10,
    0x0, 0x0, 0x8, 0xf5, 0x5, 0x20, 0x0, 0x7f,
    0x62, 0xfb, 0x0, 0xc, 0xf3, 0x9, 0xfe, 0xad,
    0xfb, 0x0, 0x6, 0xdf, 0xe8, 0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x6f, 0x70, 0x0, 0x0, 0x0, 0xee,
    0x0, 0x0, 0x0, 0x6, 0xf6, 0x0, 0x0, 0x0,
    0xe, 0xd0, 0x0, 0x0, 0x0, 0x7f, 0x52, 0x83,
    0x0, 0x0, 0xed, 0x4, 0xf6, 0x0, 0x7, 0xf5,
    0x4, 0xf6, 0x0, 0x1e, 0xd0, 0x4, 0xf6, 0x0,
    0x6f, 0xff, 0xff, 0xff, 0xf1, 0x39, 0x99, 0x9a,
    0xfb, 0x90, 0x0, 0x0, 0x4, 0xf6, 0x0, 0x0,
    0x0, 0x4, 0xf6, 0x0,

    /* U+0035 "5" */
    0xf, 0xff, 0xff, 0xd0, 0x1f, 0xd9, 0x99, 0x70,
    0x3f, 0x70, 0x0, 0x0, 0x5f, 0x50, 0x0, 0x0,
    0x7f, 0xcf, 0xe8, 0x0, 0x9f, 0xda, 0xef, 0xa0,
    0x49, 0x0, 0xd, 0xf2, 0x0, 0x0, 0x8, 0xf5,
    0x1, 0x0, 0x7, 0xf5, 0x9d, 0x0, 0xd, 0xf2,
    0x6f, 0xea, 0xef, 0x90, 0x6, 0xdf, 0xd7, 0x0,

    /* U+0036 "6" */
    0x0, 0x0, 0x5f, 0x80, 0x0, 0x0, 0xe, 0xd0,
    0x0, 0x0, 0x9, 0xf3, 0x0, 0x0, 0x3, 0xf7,
    0x0, 0x0, 0x0, 0xcf, 0xee, 0xa1, 0x0, 0x5f,
    0xfa, 0xcf, 0xd0, 0xd, 0xf2, 0x0, 0xaf, 0x61,
    0xfb, 0x0, 0x4, 0xf9, 0x1f, 0xb0, 0x0, 0x4f,
    0x90, 0xef, 0x20, 0xa, 0xf5, 0x5, 0xff, 0xbd,
    0xfc, 0x0, 0x5, 0xcf, 0xe9, 0x0,

    /* U+0037 "7" */
    0x2f, 0xff, 0xff, 0xff, 0x61, 0x99, 0x99, 0x9d,
    0xf3, 0x0, 0x0, 0x0, 0xed, 0x0, 0x0, 0x0,
    0x5f, 0x70, 0x0, 0x0, 0xc, 0xf1, 0x0, 0x0,
    0x2, 0xfa, 0x0, 0x0, 0x0, 0x9f, 0x40, 0x0,
    0x0, 0xe, 0xd0, 0x0, 0x0, 0x6, 0xf7, 0x0,
    0x0, 0x0, 0xcf, 0x10, 0x0, 0x0, 0x3f, 0xa0,
    0x0, 0x0, 0x9, 0xf4, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x6d, 0xfe, 0x90, 0x0, 0x6f, 0xe9, 0xcf,
    0xb0, 0xc, 0xf1, 0x0, 0xbf, 0x0, 0xbf, 0x10,
    0xc, 0xf0, 0x3, 0xee, 0xad, 0xf6, 0x0, 0x1c,
    0xff, 0xfe, 0x30, 0xc, 0xf5, 0x3, 0xdf, 0x12,
    0xfb, 0x0, 0x6, 0xf6, 0x3f, 0x90, 0x0, 0x4f,
    0x81, 0xfe, 0x10, 0xa, 0xf5, 0x8, 0xfe, 0xad,
    0xfd, 0x0, 0x6, 0xdf, 0xe9, 0x10,

    /* U+0039 "9" */
    0x0, 0x6d, 0xfd, 0x70, 0x0, 0x8f, 0xeb, 0xdf,
    0xa0, 0x1f, 0xd1, 0x0, 0xcf, 0x34, 0xf8, 0x0,
    0x6, 0xf6, 0x4f, 0xa0, 0x0, 0x7f, 0x60, 0xff,
    0x50, 0x4e, 0xf2, 0x5, 0xff, 0xff, 0xfa, 0x0,
    0x2, 0x89, 0xef, 0x10, 0x0, 0x0, 0x6f, 0x70,
    0x0, 0x0, 0x1e, 0xd0, 0x0, 0x0, 0xa, 0xf4,
    0x0, 0x0, 0x3, 0xfa, 0x0, 0x0,

    /* U+003A ":" */
    0xad, 0xd, 0xf1, 0x12, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x12, 0xc, 0xf1, 0xad, 0x0,

    /* U+003B ";" */
    0x8e, 0x1a, 0xf3, 0x3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x12, 0xb, 0xf2, 0x9f, 0x50, 0xd3, 0x9b,
    0x6, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x6,
    0xd8, 0x0, 0x1, 0x8e, 0xfd, 0x40, 0x3a, 0xff,
    0xc5, 0x0, 0x3f, 0xfa, 0x30, 0x0, 0x2, 0xef,
    0xe7, 0x0, 0x0, 0x0, 0x5c, 0xff, 0x92, 0x0,
    0x0, 0x3, 0xaf, 0xf6, 0x0, 0x0, 0x0, 0x29,
    0x70,

    /* U+003D "=" */
    0x3f, 0xff, 0xff, 0xff, 0x81, 0x88, 0x88, 0x88,
    0x84, 0x0, 0x0, 0x0, 0x0, 0x1, 0x88, 0x88,
    0x88, 0x84, 0x3f, 0xff, 0xff, 0xff, 0x80,

    /* U+003E ">" */
    0x0, 0x0, 0x0, 0x0, 0x3, 0xe8, 0x10, 0x0,
    0x0, 0x1b, 0xff, 0xa3, 0x0, 0x0, 0x3, 0xaf,
    0xfc, 0x50, 0x0, 0x0, 0x18, 0xff, 0x70, 0x0,
    0x5, 0xcf, 0xf5, 0x0, 0x7e, 0xfe, 0x71, 0x2,
    0xff, 0xc5, 0x0, 0x0, 0x3a, 0x30, 0x0, 0x0,
    0x0,

    /* U+003F "?" */
    0x0, 0x5c, 0xfe, 0x80, 0x0, 0x7f, 0xeb, 0xef,
    0x90, 0xe, 0xc0, 0x0, 0xef, 0x0, 0x12, 0x0,
    0xd, 0xf0, 0x0, 0x0, 0x6, 0xf8, 0x0, 0x0,
    0x6, 0xfa, 0x0, 0x0, 0x3, 0xfa, 0x0, 0x0,
    0x0, 0x7f, 0x10, 0x0, 0x0, 0x7, 0xe0, 0x0,
    0x0, 0x0, 0x13, 0x0, 0x0, 0x0, 0x0, 0x20,
    0x0, 0x0, 0x0, 0x7f, 0x50, 0x0, 0x0, 0x5,
    0xf4, 0x0, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x29, 0xef, 0xff, 0xb4, 0x0, 0x0,
    0x0, 0x7, 0xfe, 0x85, 0x57, 0xdf, 0x90, 0x0,
    0x0, 0x7f, 0x80, 0x0, 0x0, 0x7, 0xf8, 0x0,
    0x3, 0xf8, 0x0, 0x48, 0x60, 0x83, 0x8f, 0x30,
    0x9, 0xd0, 0x8, 0xff, 0xfb, 0xf5, 0xe, 0x90,
    0xf, 0x70, 0x3f, 0xa1, 0x3e, 0xf5, 0x9, 0xe0,
    0x1f, 0x40, 0x8f, 0x10, 0x6, 0xf5, 0x6, 0xf0,
    0x3f, 0x20, 0xaf, 0x0, 0x3, 0xf5, 0x4, 0xf1,
    0x2f, 0x30, 0xaf, 0x0, 0x3, 0xf5, 0x5, 0xf0,
    0xf, 0x60, 0x7f, 0x20, 0x6, 0xf6, 0x7, 0xf0,
    0xb, 0xc0, 0x1f, 0xc3, 0x4e, 0xfb, 0x3d, 0x90,
    0x5, 0xf5, 0x5, 0xff, 0xf8, 0x6f, 0xfe, 0x20,
    0x0, 0xaf, 0x40, 0x14, 0x20, 0x2, 0x40, 0x0,
    0x0, 0x1b, 0xfb, 0x53, 0x24, 0x97, 0x0, 0x0,
    0x0, 0x0, 0x6d, 0xff, 0xff, 0xd5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x13, 0x42, 0x0, 0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x8f, 0x40, 0x0, 0x0, 0x0, 0xe,
    0xfb, 0x0, 0x0, 0x0, 0x4, 0xfc, 0xf1, 0x0,
    0x0, 0x0, 0xad, 0x3f, 0x70, 0x0, 0x0, 0x1f,
    0x70, 0xcd, 0x0, 0x0, 0x7, 0xf1, 0x6, 0xf4,
    0x0, 0x0, 0xdb, 0x0, 0xf, 0xa0, 0x0, 0x3f,
    0xff, 0xff, 0xff, 0x10, 0x9, 0xf9, 0x99, 0x9a,
    0xf7, 0x0, 0xea, 0x0, 0x0, 0xe, 0xd0, 0x5f,
    0x50, 0x0, 0x0, 0x9f, 0x3b, 0xf0, 0x0, 0x0,
    0x3, 0xf9,

    /* U+0042 "B" */
    0xaf, 0xff, 0xfd, 0x70, 0xa, 0xf9, 0x9a, 0xef,
    0xc0, 0xaf, 0x0, 0x0, 0xcf, 0x3a, 0xf0, 0x0,
    0x8, 0xf4, 0xaf, 0x0, 0x5, 0xfe, 0x1a, 0xff,
    0xff, 0xff, 0x40, 0xaf, 0x88, 0x8a, 0xff, 0x4a,
    0xf0, 0x0, 0x2, 0xfc, 0xaf, 0x0, 0x0, 0xe,
    0xfa, 0xf0, 0x0, 0x3, 0xfd, 0xaf, 0x99, 0x9b,
    0xff, 0x6a, 0xff, 0xff, 0xeb, 0x40,

    /* U+0043 "C" */
    0x0, 0x4, 0xbe, 0xfc, 0x50, 0x0, 0x7, 0xff,
    0xcb, 0xff, 0x80, 0x4, 0xfd, 0x20, 0x1, 0xce,
    0x10, 0xcf, 0x20, 0x0, 0x1, 0x10, 0x1f, 0xc0,
    0x0, 0x0, 0x0, 0x3, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0x90, 0x0, 0x0, 0x0, 0x1, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xf2, 0x0, 0x0,
    0x10, 0x0, 0x4f, 0xd2, 0x0, 0xc, 0xe1, 0x0,
    0x7f, 0xfb, 0xbe, 0xf8, 0x0, 0x0, 0x4b, 0xef,
    0xc5, 0x0,

    /* U+0044 "D" */
    0xaf, 0xff, 0xec, 0x60, 0x0, 0xaf, 0x99, 0xae,
    0xfc, 0x10, 0xaf, 0x10, 0x0, 0x8f, 0xc0, 0xaf,
    0x10, 0x0, 0xa, 0xf4, 0xaf, 0x10, 0x0, 0x3,
    0xf9, 0xaf, 0x10, 0x0, 0x0, 0xfb, 0xaf, 0x10,
    0x0, 0x0, 0xfb, 0xaf, 0x10, 0x0, 0x3, 0xf9,
    0xaf, 0x10, 0x0, 0xa, 0xf4, 0xaf, 0x10, 0x0,
    0x8f, 0xc0, 0xaf, 0x99, 0xae, 0xfd, 0x10, 0xaf,
    0xff, 0xec, 0x60, 0x0,

    /* U+0045 "E" */
    0xaf, 0xff, 0xff, 0xfa, 0xaf, 0xa9, 0x99, 0x96,
    0xaf, 0x10, 0x0, 0x0, 0xaf, 0x10, 0x0, 0x0,
    0xaf, 0x10, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xf1,
    0xaf, 0x99, 0x99, 0x90, 0xaf, 0x10, 0x0, 0x0,
    0xaf, 0x10, 0x0, 0x0, 0xaf, 0x10, 0x0, 0x0,
    0xaf, 0xa9, 0x99, 0x98, 0xaf, 0xff, 0xff, 0xfd,

    /* U+0046 "F" */
    0xaf, 0xff, 0xff, 0xfa, 0xaf, 0xa9, 0x99, 0x96,
    0xaf, 0x10, 0x0, 0x0, 0xaf, 0x10, 0x0, 0x0,
    0xaf, 0x10, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xf1,
    0xaf, 0xa9, 0x99, 0x90, 0xaf, 0x10, 0x0, 0x0,
    0xaf, 0x10, 0x0, 0x0, 0xaf, 0x10, 0x0, 0x0,
    0xaf, 0x10, 0x0, 0x0, 0xaf, 0x10, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x3, 0xae, 0xfd, 0x81, 0x0, 0x6, 0xff,
    0xcb, 0xdf, 0xe1, 0x4, 0xfd, 0x20, 0x0, 0x5c,
    0x30, 0xcf, 0x30, 0x0, 0x0, 0x0, 0x1f, 0xc0,
    0x0, 0x0, 0x0, 0x3, 0xf9, 0x0, 0x8, 0xff,
    0xf9, 0x3f, 0x90, 0x0, 0x49, 0xaf, 0x91, 0xfc,
    0x0, 0x0, 0x2, 0xf9, 0xb, 0xf3, 0x0, 0x0,
    0x2f, 0x90, 0x3f, 0xe3, 0x0, 0x5, 0xf9, 0x0,
    0x6f, 0xfd, 0xbd, 0xff, 0x40, 0x0, 0x3a, 0xef,
    0xe9, 0x20,

    /* U+0048 "H" */
    0xaf, 0x10, 0x0, 0x0, 0xfb, 0xaf, 0x10, 0x0,
    0x0, 0xfb, 0xaf, 0x10, 0x0, 0x0, 0xfb, 0xaf,
    0x10, 0x0, 0x0, 0xfb, 0xaf, 0x10, 0x0, 0x0,
    0xfb, 0xaf, 0xff, 0xff, 0xff, 0xfb, 0xaf, 0xaa,
    0xaa, 0xaa, 0xfb, 0xaf, 0x10, 0x0, 0x0, 0xfb,
    0xaf, 0x10, 0x0, 0x0, 0xfb, 0xaf, 0x10, 0x0,
    0x0, 0xfb, 0xaf, 0x10, 0x0, 0x0, 0xfb, 0xaf,
    0x10, 0x0, 0x0, 0xfb,

    /* U+0049 "I" */
    0xaf, 0x1a, 0xf1, 0xaf, 0x1a, 0xf1, 0xaf, 0x1a,
    0xf1, 0xaf, 0x1a, 0xf1, 0xaf, 0x1a, 0xf1, 0xaf,
    0x1a, 0xf1,

    /* U+004A "J" */
    0x0, 0x0, 0x4f, 0x70, 0x0, 0x4, 0xf7, 0x0,
    0x0, 0x4f, 0x70, 0x0, 0x4, 0xf7, 0x0, 0x0,
    0x4f, 0x70, 0x0, 0x4, 0xf7, 0x0, 0x0, 0x4f,
    0x70, 0x0, 0x4, 0xf7, 0x0, 0x0, 0x5f, 0x69,
    0xa0, 0x9, 0xf4, 0xaf, 0xcc, 0xfd, 0x0, 0x9e,
    0xea, 0x10,

    /* U+004B "K" */
    0xaf, 0x10, 0x0, 0x1d, 0xf4, 0xa, 0xf1, 0x0,
    0xc, 0xf5, 0x0, 0xaf, 0x10, 0xb, 0xf6, 0x0,
    0xa, 0xf1, 0x9, 0xf7, 0x0, 0x0, 0xaf, 0x18,
    0xf8, 0x0, 0x0, 0xa, 0xf7, 0xff, 0x50, 0x0,
    0x0, 0xaf, 0xfb, 0xff, 0x20, 0x0, 0xa, 0xfb,
    0x5, 0xfc, 0x0, 0x0, 0xaf, 0x10, 0xa, 0xf8,
    0x0, 0xa, 0xf1, 0x0, 0xd, 0xf4, 0x0, 0xaf,
    0x10, 0x0, 0x3f, 0xe1, 0xa, 0xf1, 0x0, 0x0,
    0x7f, 0xb0,

    /* U+004C "L" */
    0xaf, 0x10, 0x0, 0x0, 0xaf, 0x10, 0x0, 0x0,
    0xaf, 0x10, 0x0, 0x0, 0xaf, 0x10, 0x0, 0x0,
    0xaf, 0x10, 0x0, 0x0, 0xaf, 0x10, 0x0, 0x0,
    0xaf, 0x10, 0x0, 0x0, 0xaf, 0x10, 0x0, 0x0,
    0xaf, 0x10, 0x0, 0x0, 0xaf, 0x10, 0x0, 0x0,
    0xaf, 0xaa, 0xaa, 0xa6, 0xaf, 0xff, 0xff, 0xfb,

    /* U+004D "M" */
    0xaf, 0x20, 0x0, 0x0, 0x2, 0xf9, 0xaf, 0xb0,
    0x0, 0x0, 0xb, 0xf9, 0xaf, 0xf5, 0x0, 0x0,
    0x5f, 0xf9, 0xaf, 0xde, 0x0, 0x0, 0xed, 0xf9,
    0xaf, 0x4f, 0x80, 0x9, 0xf4, 0xf9, 0xaf, 0xa,
    0xf2, 0x3f, 0x81, 0xf9, 0xaf, 0x1, 0xec, 0xcd,
    0x1, 0xf9, 0xaf, 0x0, 0x6f, 0xf4, 0x1, 0xf9,
    0xaf, 0x0, 0xb, 0x90, 0x1, 0xf9, 0xaf, 0x0,
    0x0, 0x0, 0x1, 0xf9, 0xaf, 0x0, 0x0, 0x0,
    0x1, 0xf9, 0xaf, 0x0, 0x0, 0x0, 0x1, 0xf9,

    /* U+004E "N" */
    0xaf, 0x20, 0x0, 0x0, 0xf9, 0xaf, 0xc0, 0x0,
    0x0, 0xf9, 0xaf, 0xf8, 0x0, 0x0, 0xf9, 0xaf,
    0xcf, 0x30, 0x0, 0xf9, 0xaf, 0x1f, 0xd0, 0x0,
    0xf9, 0xaf, 0x5, 0xf9, 0x0, 0xf9, 0xaf, 0x0,
    0xaf, 0x50, 0xf9, 0xaf, 0x0, 0x1e, 0xe1, 0xf9,
    0xaf, 0x0, 0x4, 0xfb, 0xf9, 0xaf, 0x0, 0x0,
    0x9f, 0xf9, 0xaf, 0x0, 0x0, 0xd, 0xf9, 0xaf,
    0x0, 0x0, 0x3, 0xf9,

    /* U+004F "O" */
    0x0, 0x4, 0xbe, 0xfc, 0x60, 0x0, 0x0, 0x8f,
    0xfc, 0xbe, 0xfc, 0x0, 0x5, 0xfd, 0x20, 0x0,
    0xaf, 0x90, 0xc, 0xf2, 0x0, 0x0, 0xd, 0xf1,
    0x1f, 0xc0, 0x0, 0x0, 0x7, 0xf5, 0x3f, 0x90,
    0x0, 0x0, 0x5, 0xf7, 0x3f, 0x90, 0x0, 0x0,
    0x5, 0xf7, 0x1f, 0xc0, 0x0, 0x0, 0x7, 0xf5,
    0xc, 0xf2, 0x0, 0x0, 0xd, 0xf1, 0x5, 0xfd,
    0x10, 0x0, 0xaf, 0x90, 0x0, 0x8f, 0xfb, 0xbe,
    0xfb, 0x0, 0x0, 0x4, 0xbe, 0xfc, 0x60, 0x0,

    /* U+0050 "P" */
    0xaf, 0xff, 0xfc, 0x60, 0xa, 0xf9, 0x9a, 0xef,
    0x80, 0xaf, 0x10, 0x1, 0xef, 0x1a, 0xf1, 0x0,
    0x8, 0xf5, 0xaf, 0x10, 0x0, 0xaf, 0x4a, 0xf1,
    0x1, 0x6f, 0xe0, 0xaf, 0xff, 0xff, 0xe3, 0xa,
    0xf9, 0x98, 0x50, 0x0, 0xaf, 0x10, 0x0, 0x0,
    0xa, 0xf1, 0x0, 0x0, 0x0, 0xaf, 0x10, 0x0,
    0x0, 0xa, 0xf1, 0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x4, 0xbe, 0xfc, 0x60, 0x0, 0x0, 0x8,
    0xff, 0xcb, 0xef, 0xc0, 0x0, 0x5, 0xfd, 0x20,
    0x0, 0xaf, 0x90, 0x0, 0xcf, 0x20, 0x0, 0x0,
    0xdf, 0x10, 0x1f, 0xc0, 0x0, 0x0, 0x7, 0xf5,
    0x3, 0xf9, 0x0, 0x0, 0x0, 0x5f, 0x70, 0x3f,
    0x90, 0x0, 0x0, 0x5, 0xf7, 0x1, 0xfc, 0x0,
    0x0, 0x0, 0x7f, 0x50, 0xc, 0xf2, 0x0, 0x0,
    0xd, 0xf1, 0x0, 0x5f, 0xd1, 0x0, 0xa, 0xf9,
    0x0, 0x0, 0x8f, 0xfb, 0xbe, 0xfb, 0x0, 0x0,
    0x0, 0x4b, 0xef, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xef, 0x60, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x77, 0x20,

    /* U+0052 "R" */
    0xaf, 0xff, 0xfc, 0x60, 0x0, 0xaf, 0x99, 0xae,
    0xf9, 0x0, 0xaf, 0x10, 0x0, 0xdf, 0x20, 0xaf,
    0x10, 0x0, 0x8f, 0x40, 0xaf, 0x10, 0x0, 0xcf,
    0x20, 0xaf, 0x99, 0xae, 0xf9, 0x0, 0xaf, 0xff,
    0xff, 0x60, 0x0, 0xaf, 0x10, 0x7f, 0x80, 0x0,
    0xaf, 0x10, 0xd, 0xf3, 0x0, 0xaf, 0x10, 0x3,
    0xfd, 0x0, 0xaf, 0x10, 0x0, 0x8f, 0x80, 0xaf,
    0x10, 0x0, 0xd, 0xf3,

    /* U+0053 "S" */
    0x0, 0x7d, 0xfe, 0xa2, 0x0, 0xaf, 0xea, 0xcf,
    0xf2, 0x2f, 0xc0, 0x0, 0x4f, 0x73, 0xf9, 0x0,
    0x0, 0x20, 0xe, 0xf7, 0x0, 0x0, 0x0, 0x3c,
    0xff, 0xb5, 0x0, 0x0, 0x2, 0x8d, 0xfd, 0x10,
    0x0, 0x0, 0x8, 0xfa, 0x28, 0x0, 0x0, 0xf,
    0xd8, 0xf8, 0x0, 0x3, 0xfb, 0x1c, 0xfe, 0xbb,
    0xff, 0x40, 0x7, 0xdf, 0xfb, 0x30,

    /* U+0054 "T" */
    0xdf, 0xff, 0xff, 0xff, 0xf2, 0x89, 0x9b, 0xfc,
    0x99, 0x91, 0x0, 0x3, 0xf8, 0x0, 0x0, 0x0,
    0x3, 0xf8, 0x0, 0x0, 0x0, 0x3, 0xf8, 0x0,
    0x0, 0x0, 0x3, 0xf8, 0x0, 0x0, 0x0, 0x3,
    0xf8, 0x0, 0x0, 0x0, 0x3, 0xf8, 0x0, 0x0,
    0x0, 0x3, 0xf8, 0x0, 0x0, 0x0, 0x3, 0xf8,
    0x0, 0x0, 0x0, 0x3, 0xf8, 0x0, 0x0, 0x0,
    0x3, 0xf8, 0x0, 0x0,

    /* U+0055 "U" */
    0xcf, 0x0, 0x0, 0x1, 0xfa, 0xcf, 0x0, 0x0,
    0x1, 0xfa, 0xcf, 0x0, 0x0, 0x1, 0xfa, 0xcf,
    0x0, 0x0, 0x1, 0xfa, 0xcf, 0x0, 0x0, 0x1,
    0xfa, 0xcf, 0x0, 0x0, 0x1, 0xfa, 0xcf, 0x0,
    0x0, 0x1, 0xfa, 0xbf, 0x0, 0x0, 0x3, 0xf9,
    0x9f, 0x40, 0x0, 0x7, 0xf7, 0x4f, 0xc1, 0x0,
    0x2e, 0xf2, 0xa, 0xff, 0xbc, 0xff, 0x70, 0x0,
    0x6c, 0xff, 0xc5, 0x0,

    /* U+0056 "V" */
    0xbf, 0x30, 0x0, 0x0, 0x3f, 0x84, 0xf9, 0x0,
    0x0, 0x9, 0xf2, 0xe, 0xe0, 0x0, 0x0, 0xec,
    0x0, 0x8f, 0x50, 0x0, 0x4f, 0x60, 0x2, 0xfa,
    0x0, 0xa, 0xf1, 0x0, 0xc, 0xf1, 0x0, 0xfa,
    0x0, 0x0, 0x5f, 0x60, 0x5f, 0x40, 0x0, 0x0,
    0xec, 0xb, 0xe0, 0x0, 0x0, 0x9, 0xf3, 0xf8,
    0x0, 0x0, 0x0, 0x3f, 0xdf, 0x20, 0x0, 0x0,
    0x0, 0xcf, 0xc0, 0x0, 0x0, 0x0, 0x6, 0xf6,
    0x0, 0x0,

    /* U+0057 "W" */
    0xbf, 0x10, 0x0, 0xe, 0xc0, 0x0, 0x4, 0xf6,
    0x6f, 0x60, 0x0, 0x3f, 0xf1, 0x0, 0x8, 0xf1,
    0x1f, 0xa0, 0x0, 0x8f, 0xf5, 0x0, 0xd, 0xc0,
    0xc, 0xf0, 0x0, 0xcd, 0xfa, 0x0, 0x1f, 0x70,
    0x7, 0xf4, 0x1, 0xf7, 0xbf, 0x0, 0x5f, 0x30,
    0x2, 0xf9, 0x5, 0xf2, 0x6f, 0x40, 0xae, 0x0,
    0x0, 0xde, 0x9, 0xe0, 0x1f, 0x90, 0xe9, 0x0,
    0x0, 0x8f, 0x2e, 0x90, 0xc, 0xe3, 0xf4, 0x0,
    0x0, 0x3f, 0xaf, 0x40, 0x7, 0xfa, 0xf0, 0x0,
    0x0, 0xe, 0xff, 0x0, 0x2, 0xff, 0xb0, 0x0,
    0x0, 0x9, 0xfa, 0x0, 0x0, 0xdf, 0x60, 0x0,
    0x0, 0x4, 0xf6, 0x0, 0x0, 0x8f, 0x10, 0x0,

    /* U+0058 "X" */
    0x5f, 0xb0, 0x0, 0x0, 0xcf, 0x30, 0xaf, 0x60,
    0x0, 0x7f, 0x70, 0x1, 0xef, 0x20, 0x2f, 0xc0,
    0x0, 0x4, 0xfc, 0xd, 0xf2, 0x0, 0x0, 0x9,
    0xfd, 0xf6, 0x0, 0x0, 0x0, 0xd, 0xfb, 0x0,
    0x0, 0x0, 0x1, 0xef, 0xc0, 0x0, 0x0, 0x0,
    0xaf, 0xbf, 0x80, 0x0, 0x0, 0x6f, 0x80, 0xdf,
    0x30, 0x0, 0x2f, 0xd0, 0x2, 0xfd, 0x0, 0xc,
    0xf3, 0x0, 0x7, 0xf9, 0x7, 0xf7, 0x0, 0x0,
    0xc, 0xf5,

    /* U+0059 "Y" */
    0x9f, 0x60, 0x0, 0x1, 0xfc, 0x1, 0xee, 0x10,
    0x0, 0x9f, 0x30, 0x6, 0xf9, 0x0, 0x2f, 0xa0,
    0x0, 0xc, 0xf2, 0xa, 0xf1, 0x0, 0x0, 0x3f,
    0xb3, 0xf7, 0x0, 0x0, 0x0, 0x9f, 0xee, 0x0,
    0x0, 0x0, 0x1, 0xef, 0x50, 0x0, 0x0, 0x0,
    0xb, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf0,
    0x0, 0x0,

    /* U+005A "Z" */
    0x2f, 0xff, 0xff, 0xff, 0xb1, 0x99, 0x99, 0x9e,
    0xf6, 0x0, 0x0, 0x3, 0xfb, 0x0, 0x0, 0x0,
    0xcf, 0x20, 0x0, 0x0, 0x7f, 0x70, 0x0, 0x0,
    0x2f, 0xd0, 0x0, 0x0, 0xb, 0xf3, 0x0, 0x0,
    0x6, 0xf8, 0x0, 0x0, 0x1, 0xee, 0x0, 0x0,
    0x0, 0xaf, 0x40, 0x0, 0x0, 0x4f, 0xfa, 0xaa,
    0xaa, 0x9a, 0xff, 0xff, 0xff, 0xff,

    /* U+005B "[" */
    0x69, 0x99, 0x3a, 0xff, 0xf6, 0xaf, 0x0, 0xa,
    0xf0, 0x0, 0xaf, 0x0, 0xa, 0xf0, 0x0, 0xaf,
    0x0, 0xa, 0xf0, 0x0, 0xaf, 0x0, 0xa, 0xf0,
    0x0, 0xaf, 0x0, 0xa, 0xf0, 0x0, 0xaf, 0x0,
    0xa, 0xf0, 0x0, 0xaf, 0x99, 0x3a, 0xff, 0xf6,

    /* U+005C "\\" */
    0x9f, 0x0, 0x0, 0x3, 0xf6, 0x0, 0x0, 0xd,
    0xc0, 0x0, 0x0, 0x8f, 0x10, 0x0, 0x2, 0xf7,
    0x0, 0x0, 0xc, 0xd0, 0x0, 0x0, 0x6f, 0x30,
    0x0, 0x1, 0xf9, 0x0, 0x0, 0xa, 0xe0, 0x0,
    0x0, 0x4f, 0x40, 0x0, 0x0, 0xea, 0x0, 0x0,
    0x9, 0xf1,

    /* U+005D "]" */
    0x69, 0x99, 0x3b, 0xff, 0xf5, 0x0, 0x4f, 0x50,
    0x4, 0xf5, 0x0, 0x4f, 0x50, 0x4, 0xf5, 0x0,
    0x4f, 0x50, 0x4, 0xf5, 0x0, 0x4f, 0x50, 0x4,
    0xf5, 0x0, 0x4f, 0x50, 0x4, 0xf5, 0x0, 0x4f,
    0x50, 0x4, 0xf5, 0x69, 0xbf, 0x5b, 0xff, 0xf5,

    /* U+005E "^" */
    0x0, 0x7, 0x60, 0x0, 0x0, 0x3f, 0xf1, 0x0,
    0x0, 0xae, 0xf8, 0x0, 0x1, 0xf8, 0xae, 0x0,
    0x7, 0xf2, 0x3f, 0x50, 0xe, 0xb0, 0xd, 0xc0,
    0x5f, 0x40, 0x6, 0xf2,

    /* U+005F "_" */
    0x22, 0x22, 0x22, 0x2f, 0xff, 0xff, 0xff, 0x55,
    0x55, 0x55, 0x40,

    /* U+0060 "`" */
    0x6, 0x60, 0x0, 0x5f, 0x40, 0x0, 0xac, 0x0,

    /* U+0061 "a" */
    0x0, 0x6d, 0xfc, 0x40, 0x7, 0xfb, 0x9e, 0xf3,
    0x6, 0x90, 0x2, 0xfa, 0x0, 0x0, 0x0, 0xec,
    0x2, 0xbf, 0xfe, 0xfc, 0xe, 0xe6, 0x46, 0xec,
    0x2f, 0x80, 0x0, 0xfc, 0xf, 0xd5, 0x4b, 0xfc,
    0x3, 0xcf, 0xe9, 0xac,

    /* U+0062 "b" */
    0xdd, 0x0, 0x0, 0x0, 0xd, 0xd0, 0x0, 0x0,
    0x0, 0xdd, 0x0, 0x0, 0x0, 0xd, 0xd0, 0x0,
    0x0, 0x0, 0xdd, 0x4d, 0xfc, 0x40, 0xd, 0xef,
    0xca, 0xff, 0x30, 0xdf, 0x70, 0x3, 0xfc, 0xd,
    0xf0, 0x0, 0xb, 0xf0, 0xdd, 0x0, 0x0, 0x9f,
    0x1d, 0xf0, 0x0, 0xb, 0xf0, 0xdf, 0x60, 0x2,
    0xfc, 0xd, 0xdf, 0xba, 0xff, 0x30, 0xda, 0x4d,
    0xfc, 0x40, 0x0,

    /* U+0063 "c" */
    0x0, 0x4c, 0xfe, 0x80, 0x5, 0xfe, 0xac, 0xf8,
    0xe, 0xe1, 0x0, 0x61, 0x3f, 0x90, 0x0, 0x0,
    0x4f, 0x70, 0x0, 0x0, 0x3f, 0x90, 0x0, 0x0,
    0xe, 0xe1, 0x0, 0x61, 0x5, 0xfe, 0xac, 0xf8,
    0x0, 0x4c, 0xfe, 0x80,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0xf, 0xa0, 0x0, 0x0, 0x0,
    0xfa, 0x0, 0x0, 0x0, 0xf, 0xa0, 0x0, 0x0,
    0x0, 0xfa, 0x0, 0x5d, 0xfc, 0x2f, 0xa0, 0x6f,
    0xea, 0xde, 0xfa, 0xe, 0xe1, 0x0, 0x9f, 0xa3,
    0xf8, 0x0, 0x2, 0xfa, 0x4f, 0x60, 0x0, 0xf,
    0xa3, 0xf8, 0x0, 0x1, 0xfa, 0xe, 0xd0, 0x0,
    0x7f, 0xa0, 0x6f, 0xc6, 0x9f, 0xfa, 0x0, 0x6d,
    0xfc, 0x3d, 0xa0,

    /* U+0065 "e" */
    0x0, 0x4c, 0xfe, 0x70, 0x0, 0x5f, 0xe9, 0xcf,
    0x70, 0xe, 0xe1, 0x0, 0xcf, 0x3, 0xf9, 0x11,
    0x18, 0xf3, 0x4f, 0xff, 0xff, 0xff, 0x43, 0xfa,
    0x44, 0x44, 0x40, 0xe, 0xd0, 0x0, 0x32, 0x0,
    0x5f, 0xe9, 0xaf, 0xc0, 0x0, 0x4c, 0xfe, 0x91,
    0x0,

    /* U+0066 "f" */
    0x0, 0x2c, 0xfc, 0x10, 0xd, 0xfa, 0xb0, 0x3,
    0xf7, 0x0, 0x0, 0x4f, 0x50, 0x0, 0xcf, 0xff,
    0xf8, 0x5, 0xaf, 0xa7, 0x30, 0x4, 0xf5, 0x0,
    0x0, 0x4f, 0x50, 0x0, 0x4, 0xf5, 0x0, 0x0,
    0x4f, 0x50, 0x0, 0x4, 0xf5, 0x0, 0x0, 0x4f,
    0x50, 0x0, 0x4, 0xf5, 0x0, 0x0,

    /* U+0067 "g" */
    0x0, 0x5d, 0xfc, 0x3c, 0xa0, 0x6f, 0xea, 0xde,
    0xea, 0xe, 0xe1, 0x0, 0x9f, 0xa3, 0xf8, 0x0,
    0x1, 0xfa, 0x4f, 0x60, 0x0, 0xf, 0xa3, 0xf8,
    0x0, 0x1, 0xfa, 0xe, 0xe1, 0x0, 0x9f, 0xa0,
    0x6f, 0xe9, 0xce, 0xfa, 0x0, 0x5d, 0xfc, 0x3f,
    0xa0, 0x0, 0x0, 0x1, 0xf8, 0x8, 0x90, 0x0,
    0x8f, 0x40, 0x9f, 0xda, 0xcf, 0xb0, 0x0, 0x7d,
    0xfe, 0x80, 0x0,

    /* U+0068 "h" */
    0xdd, 0x0, 0x0, 0x0, 0xdd, 0x0, 0x0, 0x0,
    0xdd, 0x0, 0x0, 0x0, 0xdd, 0x0, 0x0, 0x0,
    0xdd, 0x5d, 0xfb, 0x20, 0xde, 0xfb, 0xbf, 0xd0,
    0xdf, 0x50, 0xa, 0xf3, 0xde, 0x0, 0x5, 0xf5,
    0xdd, 0x0, 0x4, 0xf6, 0xdd, 0x0, 0x4, 0xf6,
    0xdd, 0x0, 0x4, 0xf6, 0xdd, 0x0, 0x4, 0xf6,
    0xdd, 0x0, 0x4, 0xf6,

    /* U+0069 "i" */
    0xc, 0xc0, 0xe, 0xe0, 0x1, 0x10, 0xd, 0xd0,
    0xd, 0xd0, 0xd, 0xd0, 0xd, 0xd0, 0xd, 0xd0,
    0xd, 0xd0, 0xd, 0xd0, 0xd, 0xd0, 0xd, 0xd0,

    /* U+006A "j" */
    0x0, 0x0, 0xcc, 0x0, 0x0, 0xe, 0xe0, 0x0,
    0x0, 0x11, 0x0, 0x0, 0xd, 0xd0, 0x0, 0x0,
    0xdd, 0x0, 0x0, 0xd, 0xd0, 0x0, 0x0, 0xdd,
    0x0, 0x0, 0xd, 0xd0, 0x0, 0x0, 0xdd, 0x0,
    0x0, 0xd, 0xd0, 0x0, 0x0, 0xdd, 0x0, 0x0,
    0xd, 0xd0, 0x0, 0x0, 0xdc, 0x0, 0x0, 0xf,
    0xa0, 0x8, 0xbd, 0xf5, 0x0, 0x9e, 0xe7, 0x0,

    /* U+006B "k" */
    0xdd, 0x0, 0x0, 0x0, 0xdd, 0x0, 0x0, 0x0,
    0xdd, 0x0, 0x0, 0x0, 0xdd, 0x0, 0x0, 0x0,
    0xdd, 0x0, 0x1e, 0xe1, 0xdd, 0x0, 0xcf, 0x20,
    0xdd, 0x9, 0xf4, 0x0, 0xdd, 0x6f, 0x80, 0x0,
    0xde, 0xff, 0xd0, 0x0, 0xdf, 0xc8, 0xf7, 0x0,
    0xde, 0x10, 0xdf, 0x20, 0xdd, 0x0, 0x4f, 0xb0,
    0xdd, 0x0, 0x9, 0xf6,

    /* U+006C "l" */
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd,

    /* U+006D "m" */
    0xda, 0x7e, 0xf9, 0x7, 0xee, 0x70, 0xdf, 0xd7,
    0xcf, 0xcd, 0x7c, 0xf4, 0xdf, 0x20, 0x1f, 0xf1,
    0x2, 0xf9, 0xdd, 0x0, 0xe, 0xd0, 0x0, 0xeb,
    0xdc, 0x0, 0xd, 0xc0, 0x0, 0xeb, 0xdc, 0x0,
    0xd, 0xc0, 0x0, 0xeb, 0xdc, 0x0, 0xd, 0xc0,
    0x0, 0xeb, 0xdc, 0x0, 0xd, 0xc0, 0x0, 0xeb,
    0xdc, 0x0, 0xd, 0xc0, 0x0, 0xeb,

    /* U+006E "n" */
    0xda, 0x6d, 0xfc, 0x20, 0xde, 0xe8, 0x8f, 0xd0,
    0xdf, 0x30, 0x8, 0xf3, 0xde, 0x0, 0x5, 0xf5,
    0xdd, 0x0, 0x4, 0xf6, 0xdd, 0x0, 0x4, 0xf6,
    0xdd, 0x0, 0x4, 0xf6, 0xdd, 0x0, 0x4, 0xf6,
    0xdd, 0x0, 0x4, 0xf6,

    /* U+006F "o" */
    0x0, 0x3b, 0xfe, 0x80, 0x0, 0x4f, 0xea, 0xcf,
    0xc0, 0xd, 0xf1, 0x0, 0x8f, 0x63, 0xf9, 0x0,
    0x1, 0xfb, 0x4f, 0x70, 0x0, 0xf, 0xc3, 0xf9,
    0x0, 0x0, 0xfb, 0xd, 0xe1, 0x0, 0x8f, 0x60,
    0x4f, 0xea, 0xcf, 0xc0, 0x0, 0x3c, 0xfe, 0x80,
    0x0,

    /* U+0070 "p" */
    0xda, 0x5d, 0xfc, 0x40, 0xd, 0xef, 0x87, 0xdf,
    0x30, 0xdf, 0x40, 0x1, 0xfc, 0xd, 0xe0, 0x0,
    0xb, 0xf0, 0xdd, 0x0, 0x0, 0x9f, 0x1d, 0xf0,
    0x0, 0xb, 0xf0, 0xdf, 0x70, 0x3, 0xfc, 0xd,
    0xef, 0xba, 0xff, 0x30, 0xdd, 0x4d, 0xfc, 0x40,
    0xd, 0xd0, 0x0, 0x0, 0x0, 0xdd, 0x0, 0x0,
    0x0, 0xd, 0xd0, 0x0, 0x0, 0x0, 0xdd, 0x0,
    0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x5d, 0xfc, 0x3c, 0xa0, 0x6f, 0xea, 0xce,
    0xea, 0xe, 0xe1, 0x0, 0x9f, 0xa3, 0xf8, 0x0,
    0x1, 0xfa, 0x4f, 0x60, 0x0, 0xf, 0xa3, 0xf8,
    0x0, 0x1, 0xfa, 0xe, 0xe1, 0x0, 0x9f, 0xa0,
    0x6f, 0xea, 0xce, 0xfa, 0x0, 0x6d, 0xfc, 0x3f,
    0xa0, 0x0, 0x0, 0x0, 0xfa, 0x0, 0x0, 0x0,
    0xf, 0xa0, 0x0, 0x0, 0x0, 0xfa, 0x0, 0x0,
    0x0, 0xf, 0xa0,

    /* U+0072 "r" */
    0xda, 0x7e, 0xf1, 0xde, 0xe7, 0x70, 0xdf, 0x40,
    0x0, 0xde, 0x0, 0x0, 0xdd, 0x0, 0x0, 0xdd,
    0x0, 0x0, 0xdd, 0x0, 0x0, 0xdd, 0x0, 0x0,
    0xdd, 0x0, 0x0,

    /* U+0073 "s" */
    0x3, 0xcf, 0xe9, 0x1, 0xee, 0x9a, 0xf9, 0x4f,
    0x70, 0x6, 0x21, 0xfe, 0x61, 0x0, 0x3, 0xbf,
    0xfb, 0x20, 0x0, 0x17, 0xfc, 0x3a, 0x0, 0xd,
    0xe4, 0xfd, 0x8a, 0xf9, 0x5, 0xcf, 0xe9, 0x0,

    /* U+0074 "t" */
    0x3, 0x81, 0x0, 0x0, 0x7f, 0x30, 0x0, 0x7,
    0xf3, 0x0, 0xd, 0xff, 0xff, 0xa0, 0x6b, 0xf9,
    0x85, 0x0, 0x7f, 0x30, 0x0, 0x7, 0xf3, 0x0,
    0x0, 0x7f, 0x30, 0x0, 0x7, 0xf3, 0x0, 0x0,
    0x6f, 0x50, 0x0, 0x2, 0xfe, 0x99, 0x0, 0x6,
    0xef, 0xb0,

    /* U+0075 "u" */
    0xfb, 0x0, 0x6, 0xf3, 0xfb, 0x0, 0x6, 0xf3,
    0xfb, 0x0, 0x6, 0xf3, 0xfb, 0x0, 0x6, 0xf3,
    0xfb, 0x0, 0x6, 0xf3, 0xeb, 0x0, 0x7, 0xf3,
    0xce, 0x0, 0xc, 0xf3, 0x7f, 0xb7, 0xce, 0xf3,
    0x8, 0xef, 0xa5, 0xf3,

    /* U+0076 "v" */
    0xaf, 0x20, 0x0, 0x8f, 0x34, 0xf8, 0x0, 0xd,
    0xd0, 0xe, 0xe0, 0x2, 0xf7, 0x0, 0x8f, 0x40,
    0x8f, 0x10, 0x2, 0xf9, 0xd, 0xa0, 0x0, 0xb,
    0xf3, 0xf4, 0x0, 0x0, 0x5f, 0xde, 0x0, 0x0,
    0x0, 0xef, 0x80, 0x0, 0x0, 0x8, 0xf2, 0x0,
    0x0,

    /* U+0077 "w" */
    0xbf, 0x0, 0x6, 0xf3, 0x0, 0x2f, 0x65, 0xf5,
    0x0, 0xbf, 0x80, 0x7, 0xf1, 0xf, 0xa0, 0xf,
    0xfd, 0x0, 0xcb, 0x0, 0xae, 0x4, 0xf8, 0xf3,
    0x1f, 0x60, 0x5, 0xf4, 0x9d, 0x1f, 0x85, 0xf1,
    0x0, 0xf, 0x9d, 0x70, 0xbd, 0xab, 0x0, 0x0,
    0xaf, 0xf2, 0x5, 0xff, 0x60, 0x0, 0x4, 0xfd,
    0x0, 0xf, 0xf1, 0x0, 0x0, 0xe, 0x80, 0x0,
    0xac, 0x0, 0x0,

    /* U+0078 "x" */
    0x8f, 0x70, 0x2, 0xfb, 0x0, 0xdf, 0x20, 0xce,
    0x10, 0x3, 0xfc, 0x8f, 0x50, 0x0, 0x8, 0xff,
    0xa0, 0x0, 0x0, 0xf, 0xf2, 0x0, 0x0, 0x7,
    0xff, 0xb0, 0x0, 0x2, 0xfa, 0x8f, 0x50, 0x0,
    0xce, 0x10, 0xde, 0x10, 0x8f, 0x50, 0x4, 0xfb,
    0x0,

    /* U+0079 "y" */
    0xbf, 0x20, 0x0, 0x7f, 0x45, 0xf7, 0x0, 0xc,
    0xe0, 0xe, 0xd0, 0x1, 0xf8, 0x0, 0x8f, 0x30,
    0x7f, 0x20, 0x2, 0xf9, 0xc, 0xc0, 0x0, 0xc,
    0xe2, 0xf6, 0x0, 0x0, 0x6f, 0xcf, 0x0, 0x0,
    0x0, 0xff, 0xa0, 0x0, 0x0, 0x9, 0xf4, 0x0,
    0x0, 0x0, 0xae, 0x0, 0x0, 0x0, 0x2f, 0x80,
    0x0, 0x8, 0xae, 0xf1, 0x0, 0x0, 0xaf, 0xd4,
    0x0, 0x0, 0x0,

    /* U+007A "z" */
    0x3f, 0xff, 0xff, 0xe0, 0x16, 0x66, 0xaf, 0x90,
    0x0, 0x0, 0xee, 0x0, 0x0, 0x9, 0xf4, 0x0,
    0x0, 0x3f, 0xa0, 0x0, 0x0, 0xde, 0x10, 0x0,
    0x8, 0xf6, 0x0, 0x0, 0x2f, 0xe7, 0x77, 0x70,
    0x7f, 0xff, 0xff, 0xf2,

    /* U+007B "{" */
    0x0, 0x2, 0x77, 0x0, 0x4f, 0xfd, 0x0, 0x9f,
    0x50, 0x0, 0xaf, 0x0, 0x0, 0xbf, 0x0, 0x0,
    0xbf, 0x0, 0x1, 0xed, 0x0, 0x5f, 0xf5, 0x0,
    0x6f, 0xe4, 0x0, 0x3, 0xfd, 0x0, 0x0, 0xbf,
    0x0, 0x0, 0xbf, 0x0, 0x0, 0xaf, 0x0, 0x0,
    0xaf, 0x20, 0x0, 0x7f, 0xd7, 0x0, 0x9, 0xec,

    /* U+007C "|" */
    0x2b, 0x33, 0xf5, 0x3f, 0x53, 0xf5, 0x3f, 0x53,
    0xf5, 0x3f, 0x53, 0xf5, 0x3f, 0x53, 0xf5, 0x3f,
    0x53, 0xf5, 0x3f, 0x53, 0xf5, 0x3f, 0x53, 0xf5,

    /* U+007D "}" */
    0x68, 0x30, 0x0, 0xaf, 0xf6, 0x0, 0x3, 0xfb,
    0x0, 0x0, 0xec, 0x0, 0x0, 0xed, 0x0, 0x0,
    0xed, 0x0, 0x0, 0xcf, 0x20, 0x0, 0x4e, 0xf7,
    0x0, 0x2d, 0xf8, 0x0, 0xbf, 0x40, 0x0, 0xdd,
    0x0, 0x0, 0xed, 0x0, 0x0, 0xec, 0x0, 0x0,
    0xfc, 0x0, 0x6c, 0xf9, 0x0, 0xae, 0xa1, 0x0,

    /* U+007E "~" */
    0x0, 0x68, 0x10, 0x9, 0x30, 0x7f, 0xfd, 0x3,
    0xf4, 0xe, 0xa2, 0xec, 0xcf, 0x1, 0xf5, 0x3,
    0xde, 0x50,

    /* U+4E3A "为" */
    0x0, 0x3b, 0x10, 0x5f, 0x20, 0x0, 0x0, 0x0,
    0x2, 0xdd, 0x26, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xc2, 0x8f, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x5, 0x88,
    0x88, 0xee, 0x88, 0x88, 0xaf, 0x40, 0x0, 0x0,
    0xf, 0x90, 0x0, 0x4, 0xf4, 0x0, 0x0, 0x3,
    0xf6, 0x7a, 0x0, 0x4f, 0x30, 0x0, 0x0, 0x8f,
    0x17, 0xf7, 0x5, 0xf3, 0x0, 0x0, 0xe, 0xc0,
    0xa, 0xf3, 0x5f, 0x20, 0x0, 0x8, 0xf4, 0x0,
    0x1a, 0x17, 0xf1, 0x0, 0x4, 0xfb, 0x0, 0x0,
    0x0, 0x8f, 0x0, 0x3, 0xfe, 0x10, 0x0, 0x0,
    0xc, 0xd0, 0x6, 0xfe, 0x20, 0x0, 0x88, 0x79,
    0xf9, 0x0, 0xdd, 0x20, 0x0, 0x8, 0xef, 0xfc,
    0x10, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+4FE1 "信" */
    0x0, 0x1, 0x0, 0x0, 0x2, 0x10, 0x0, 0x0,
    0x0, 0x9, 0xe0, 0x0, 0xe, 0xb0, 0x0, 0x0,
    0x0, 0xe, 0xa7, 0x77, 0x7c, 0xf7, 0x77, 0x72,
    0x0, 0x5f, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0x0, 0xcf, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xfe, 0x2, 0xff, 0xff, 0xff, 0xff, 0x20,
    0x1e, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xae, 0x1, 0xff, 0xff, 0xff, 0xff, 0x10,
    0x6, 0x8e, 0x0, 0x55, 0x55, 0x55, 0x55, 0x0,
    0x0, 0x8e, 0x0, 0x44, 0x44, 0x44, 0x44, 0x0,
    0x0, 0x8e, 0x2, 0xff, 0xff, 0xff, 0xff, 0x20,
    0x0, 0x8e, 0x2, 0xf6, 0x11, 0x11, 0x5f, 0x20,
    0x0, 0x8e, 0x2, 0xf5, 0x0, 0x0, 0x4f, 0x20,
    0x0, 0x8e, 0x2, 0xff, 0xff, 0xff, 0xff, 0x20,
    0x0, 0x8e, 0x2, 0xf8, 0x55, 0x55, 0x8f, 0x20,

    /* U+5207 "切" */
    0x0, 0x5f, 0x10, 0x1, 0x11, 0x11, 0x11, 0x0,
    0x5, 0xf1, 0xa, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0x5f, 0x10, 0x58, 0xcf, 0x88, 0x8f, 0xa0, 0x5,
    0xf2, 0x31, 0x8, 0xf0, 0x0, 0xfa, 0x4a, 0xdf,
    0xff, 0x60, 0x8f, 0x0, 0xf, 0x95, 0xed, 0xf8,
    0x51, 0x9, 0xe0, 0x0, 0xf9, 0x0, 0x5f, 0x10,
    0x0, 0xbd, 0x0, 0xf, 0x80, 0x5, 0xf1, 0x0,
    0xc, 0xc0, 0x0, 0xf7, 0x0, 0x5f, 0x10, 0x0,
    0xf9, 0x0, 0x1f, 0x70, 0x5, 0xf1, 0x68, 0x4f,
    0x50, 0x2, 0xf6, 0x0, 0x5f, 0xcf, 0x9b, 0xf1,
    0x0, 0x4f, 0x40, 0xa, 0xfe, 0x56, 0xf8, 0x0,
    0x6, 0xf3, 0x0, 0x4a, 0x15, 0xfc, 0x3, 0x77,
    0xef, 0x0, 0x0, 0x1, 0xfe, 0x20, 0x3e, 0xfe,
    0x50, 0x0, 0x0, 0x3, 0x20, 0x0, 0x0, 0x0,
    0x0,

    /* U+52A1 "务" */
    0x0, 0x0, 0x6, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0x81, 0x11, 0x11, 0x11, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0x0, 0x6f, 0xfc, 0x22, 0x22, 0x5f, 0xe3, 0x0,
    0x7, 0xf8, 0x3d, 0xd3, 0x19, 0xfb, 0x10, 0x0,
    0x1, 0x30, 0x0, 0xcf, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x15, 0x9e, 0xfc, 0xaf, 0xfb, 0x74, 0x10,
    0xd, 0xff, 0xd8, 0x75, 0x1, 0x6b, 0xef, 0xf5,
    0x7, 0x62, 0x0, 0xcc, 0x0, 0x0, 0x2, 0x51,
    0x1, 0xee, 0xee, 0xff, 0xee, 0xee, 0xea, 0x0,
    0x0, 0x66, 0x69, 0xf9, 0x66, 0x66, 0xfa, 0x0,
    0x0, 0x0, 0xc, 0xe0, 0x0, 0x0, 0xf8, 0x0,
    0x0, 0x2, 0xcf, 0x40, 0x0, 0x3, 0xf6, 0x0,
    0x3, 0x9f, 0xe4, 0x0, 0x75, 0x4b, 0xf2, 0x0,
    0x6, 0xe7, 0x0, 0x0, 0xbe, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+5347 "升" */
    0x0, 0x0, 0x2, 0x48, 0xa0, 0x8f, 0x0, 0x0,
    0x5, 0xac, 0xff, 0xfd, 0xa0, 0x8f, 0x0, 0x0,
    0x6, 0xb9, 0xcf, 0x10, 0x0, 0x8f, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0x0, 0x0, 0x8f, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0x0, 0x0, 0x8f, 0x0, 0x0,
    0x8, 0x88, 0xcf, 0x88, 0x88, 0xcf, 0x88, 0x81,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0x0, 0x0, 0x9f, 0x0, 0x0, 0x8f, 0x0, 0x0,
    0x0, 0x0, 0xae, 0x0, 0x0, 0x8f, 0x0, 0x0,
    0x0, 0x0, 0xeb, 0x0, 0x0, 0x8f, 0x0, 0x0,
    0x0, 0x6, 0xf6, 0x0, 0x0, 0x8f, 0x0, 0x0,
    0x0, 0x4f, 0xd0, 0x0, 0x0, 0x8f, 0x0, 0x0,
    0x7, 0xfe, 0x20, 0x0, 0x0, 0x8f, 0x0, 0x0,
    0x6, 0xd2, 0x0, 0x0, 0x0, 0x8f, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+540E "后" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x13, 0x44, 0x56, 0x79, 0xac, 0xef, 0x20,
    0x0, 0x7f, 0xff, 0xff, 0xfe, 0xcb, 0x98, 0x20,
    0x0, 0x7f, 0x32, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x8f, 0x77, 0x77, 0x77, 0x77, 0x77, 0x70,
    0x0, 0x8e, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9d, 0x6, 0x66, 0x66, 0x66, 0x65, 0x0,
    0x0, 0xbb, 0x1f, 0xff, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0xe8, 0x1f, 0x60, 0x0, 0x0, 0x8e, 0x0,
    0x2, 0xf4, 0x1f, 0x60, 0x0, 0x0, 0x8e, 0x0,
    0x9, 0xf0, 0x1f, 0x83, 0x33, 0x33, 0xae, 0x0,
    0x2f, 0x90, 0x1f, 0xff, 0xff, 0xff, 0xfe, 0x0,
    0x1a, 0x10, 0x1f, 0x72, 0x22, 0x22, 0x9e, 0x0,

    /* U+542F "启" */
    0x0, 0x0, 0x0, 0x5, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0x40, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x7,
    0xf7, 0x66, 0x66, 0x66, 0x68, 0xf6, 0x0, 0x7f,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0x60, 0x7, 0xf6,
    0x66, 0x66, 0x66, 0x67, 0xf6, 0x0, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x60, 0x8, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xae, 0x13, 0x33,
    0x33, 0x33, 0x33, 0x0, 0xc, 0xc8, 0xff, 0xff,
    0xff, 0xff, 0xf1, 0x0, 0xf9, 0x8f, 0x22, 0x22,
    0x22, 0x8f, 0x10, 0x6f, 0x48, 0xf0, 0x0, 0x0,
    0x7, 0xf1, 0xd, 0xd0, 0x8f, 0x0, 0x0, 0x0,
    0x7f, 0x17, 0xf4, 0x8, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0x6, 0x0, 0x8f, 0x66, 0x66, 0x66, 0xaf,
    0x10,

    /* U+56DE "回" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x8f,
    0x88, 0x88, 0x88, 0x88, 0x89, 0xf6, 0x8f, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xf6, 0x8f, 0x0, 0x44,
    0x44, 0x44, 0x1, 0xf6, 0x8f, 0x3, 0xff, 0xff,
    0xff, 0x1, 0xf6, 0x8f, 0x3, 0xf3, 0x11, 0x7f,
    0x1, 0xf6, 0x8f, 0x3, 0xf2, 0x0, 0x6f, 0x1,
    0xf6, 0x8f, 0x3, 0xf2, 0x0, 0x6f, 0x1, 0xf6,
    0x8f, 0x3, 0xf8, 0x66, 0xaf, 0x1, 0xf6, 0x8f,
    0x2, 0xff, 0xff, 0xfe, 0x1, 0xf6, 0x8f, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xf6, 0x8f, 0x55, 0x55,
    0x55, 0x55, 0x56, 0xf6, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf6, 0x8f, 0x22, 0x22, 0x22, 0x22,
    0x23, 0xf6,

    /* U+5907 "备" */
    0x0, 0x0, 0x9, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0x73, 0x33, 0x33, 0x30, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0,
    0x0, 0x8f, 0xf9, 0x11, 0x11, 0x9f, 0xa0, 0x0,
    0xb, 0xf9, 0x3e, 0xc3, 0x6e, 0xe6, 0x0, 0x0,
    0x6, 0x50, 0x4, 0xff, 0xfd, 0x10, 0x0, 0x0,
    0x3, 0x6a, 0xef, 0xd7, 0xaf, 0xfc, 0x96, 0x30,
    0x4f, 0xeb, 0x72, 0x0, 0x0, 0x48, 0xcf, 0xe0,
    0x2, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x10,
    0x0, 0x8f, 0x33, 0x3f, 0x93, 0x37, 0xf3, 0x0,
    0x0, 0x8e, 0x22, 0x2e, 0x92, 0x26, 0xf3, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x0,
    0x0, 0x8e, 0x0, 0xe, 0x80, 0x4, 0xf3, 0x0,
    0x0, 0x8f, 0xee, 0xef, 0xfe, 0xee, 0xf3, 0x0,
    0x0, 0x8f, 0x66, 0x66, 0x66, 0x68, 0xf3, 0x0,

    /* U+59CB "始" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8d, 0x0, 0x0, 0x9, 0xe0, 0x0, 0x0,
    0x0, 0xac, 0x0, 0x0, 0x1f, 0x90, 0x30, 0x0,
    0x27, 0xdd, 0x77, 0x0, 0x9f, 0x13, 0xf6, 0x0,
    0x5f, 0xff, 0xff, 0x3, 0xf6, 0x0, 0xae, 0x0,
    0x0, 0xf6, 0x6f, 0x1d, 0xd3, 0x57, 0xaf, 0x70,
    0x3, 0xf3, 0x8e, 0x8f, 0xff, 0xfe, 0xcd, 0xe0,
    0x6, 0xf0, 0xab, 0x25, 0x31, 0x0, 0x2, 0x81,
    0x9, 0xd0, 0xd9, 0x6, 0x66, 0x66, 0x66, 0x20,
    0xd, 0xd2, 0xf5, 0xe, 0xff, 0xff, 0xff, 0x60,
    0x5, 0xff, 0xf1, 0xe, 0x80, 0x0, 0x1f, 0x60,
    0x0, 0x3f, 0xf3, 0xe, 0x80, 0x0, 0x1f, 0x60,
    0x0, 0x9f, 0xdf, 0x4e, 0x80, 0x0, 0x1f, 0x60,
    0x7, 0xf5, 0xc, 0x3e, 0xff, 0xff, 0xff, 0x60,
    0x1e, 0x60, 0x0, 0xe, 0xb6, 0x66, 0x7f, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+5B8C "完" */
    0x0, 0x0, 0x0, 0x4b, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xf9, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0xcd,
    0x77, 0x77, 0x77, 0x77, 0x77, 0xe9, 0xc, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0x90, 0x55, 0x56,
    0x66, 0x66, 0x66, 0x64, 0x64, 0x0, 0xb, 0xff,
    0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0x66, 0x66, 0x66,
    0x66, 0x66, 0x66, 0x50, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x0, 0x0, 0x4f, 0x50, 0x2f,
    0x60, 0x0, 0x0, 0x0, 0x8, 0xf2, 0x2, 0xf6,
    0x0, 0x8b, 0x0, 0x3, 0xfc, 0x0, 0x2f, 0x60,
    0x9, 0xd0, 0x29, 0xfe, 0x20, 0x1, 0xfc, 0x67,
    0xeb, 0xd, 0xf9, 0x10, 0x0, 0x7, 0xef, 0xfc,
    0x30, 0x21, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+5F00 "开" */
    0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,
    0x3, 0x77, 0xcf, 0x77, 0x77, 0xde, 0x77, 0x20,
    0x0, 0x0, 0x9f, 0x0, 0x0, 0xcc, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0x0, 0x0, 0xcc, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0x0, 0x0, 0xcc, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0x0, 0x0, 0xcc, 0x0, 0x0,
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x18, 0x88, 0xde, 0x88, 0x88, 0xee, 0x88, 0x80,
    0x0, 0x0, 0xcc, 0x0, 0x0, 0xcc, 0x0, 0x0,
    0x0, 0x1, 0xf8, 0x0, 0x0, 0xcc, 0x0, 0x0,
    0x0, 0x8, 0xf3, 0x0, 0x0, 0xcc, 0x0, 0x0,
    0x0, 0x4f, 0xb0, 0x0, 0x0, 0xcc, 0x0, 0x0,
    0x5, 0xfd, 0x10, 0x0, 0x0, 0xcc, 0x0, 0x0,
    0x6, 0xd2, 0x0, 0x0, 0x0, 0xcc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+5F0F "式" */
    0x0, 0x0, 0x0, 0x0, 0x5f, 0x27, 0xb1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0x31, 0xaa, 0x0,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x8, 0x88, 0x88, 0x88, 0x9f, 0xa8, 0x88, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0x60, 0x0, 0x0,
    0x0, 0x11, 0x11, 0x11, 0x1f, 0x70, 0x0, 0x0,
    0xb, 0xff, 0xff, 0xff, 0x3f, 0x90, 0x0, 0x0,
    0x4, 0x66, 0xfb, 0x66, 0x1c, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0xf8, 0x0, 0xa, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0xf8, 0x0, 0x6, 0xf2, 0x2, 0x50,
    0x0, 0x0, 0xf8, 0x24, 0x51, 0xf7, 0x7, 0xe0,
    0x17, 0x9b, 0xff, 0xff, 0xc0, 0xbe, 0xa, 0xb0,
    0x1f, 0xec, 0x97, 0x42, 0x0, 0x3f, 0xef, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xec, 0x0,

    /* U+606F "息" */
    0x0, 0x0, 0x0, 0x27, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x12, 0x22, 0x9f, 0x62, 0x22, 0x20, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x0, 0x6f, 0x11, 0x11, 0x11, 0x12, 0xf6, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x0, 0x6f, 0x11, 0x11, 0x11, 0x12, 0xf6, 0x0,
    0x0, 0x6f, 0xee, 0xee, 0xee, 0xee, 0xf6, 0x0,
    0x0, 0x6f, 0x33, 0x33, 0x33, 0x33, 0xf6, 0x0,
    0x0, 0x6f, 0x44, 0x44, 0x44, 0x44, 0xf6, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0,
    0x0, 0x51, 0x24, 0xb, 0x50, 0x0, 0x43, 0x0,
    0x2, 0xf5, 0x9e, 0x7, 0xf7, 0x33, 0xbd, 0x0,
    0x9, 0xe0, 0x9e, 0x0, 0x63, 0x8d, 0x1f, 0x90,
    0x2f, 0x70, 0x8f, 0x75, 0x56, 0xdc, 0x7, 0xf2,
    0x18, 0x0, 0x2c, 0xef, 0xfe, 0xc4, 0x0, 0x30,

    /* U+6210 "成" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xac, 0xc, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9e, 0x2, 0xd8, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0xfb, 0x77, 0x77, 0xaf, 0x77, 0x77, 0x70,
    0x0, 0xf8, 0x0, 0x0, 0x4f, 0x30, 0x26, 0x0,
    0x0, 0xfb, 0x77, 0x74, 0x2f, 0x50, 0x8f, 0x0,
    0x0, 0xff, 0xff, 0xf9, 0xf, 0x70, 0xe8, 0x0,
    0x0, 0xf7, 0x0, 0xf8, 0xc, 0xa8, 0xf1, 0x0,
    0x0, 0xf6, 0x0, 0xf7, 0x9, 0xef, 0x70, 0x0,
    0x2, 0xf5, 0x1, 0xf5, 0x5, 0xfc, 0x0, 0x51,
    0x6, 0xf4, 0x38, 0xf3, 0x1c, 0xf9, 0x0, 0xf5,
    0xa, 0xe3, 0xff, 0xc2, 0xdf, 0xbf, 0x23, 0xf2,
    0x2f, 0x90, 0x22, 0x5f, 0xe3, 0xd, 0xed, 0xd0,
    0x5e, 0x20, 0x0, 0x5c, 0x10, 0x2, 0xde, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+6362 "换" */
    0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x9d, 0x0, 0x1, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x9d, 0x0, 0xa, 0xff, 0xff, 0xf4, 0x0,
    0x17, 0xce, 0x71, 0x7f, 0x75, 0x5c, 0xe1, 0x0,
    0x2e, 0xff, 0xe9, 0xf8, 0x0, 0x5f, 0x50, 0x0,
    0x0, 0x9d, 0xe, 0xff, 0xff, 0xff, 0xff, 0x20,
    0x0, 0x9d, 0x1, 0xeb, 0x6a, 0xf6, 0x7f, 0x20,
    0x0, 0xaf, 0xf1, 0xe8, 0x7, 0xe0, 0x2f, 0x20,
    0x4d, 0xff, 0x91, 0xe8, 0x7, 0xe0, 0x2f, 0x20,
    0x4b, 0xcd, 0x3e, 0xff, 0xef, 0xfe, 0xef, 0xe4,
    0x0, 0x9d, 0x17, 0x77, 0x7e, 0xf9, 0x77, 0x72,
    0x0, 0x9d, 0x0, 0x0, 0x5f, 0xfc, 0x0, 0x0,
    0x0, 0x9d, 0x0, 0x8, 0xfc, 0x2f, 0xc2, 0x0,
    0x7, 0xdd, 0x29, 0xff, 0xa0, 0x3, 0xef, 0xa1,
    0xe, 0xf7, 0x1f, 0xa3, 0x0, 0x0, 0x19, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+63A5 "接" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0,
    0x0, 0x5f, 0x0, 0x0, 0xb, 0xe0, 0x0, 0x0,
    0x0, 0x5f, 0x0, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0x8, 0xaf, 0x83, 0x5b, 0xe5, 0x58, 0xf6, 0x30,
    0x1f, 0xff, 0xf5, 0x6, 0xf0, 0x8, 0xe0, 0x0,
    0x0, 0x5f, 0x2, 0x68, 0xf7, 0x6c, 0xd6, 0x60,
    0x0, 0x5f, 0x5, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x0, 0x5f, 0xc4, 0x0, 0x69, 0x0, 0x0, 0x0,
    0x2a, 0xff, 0xb6, 0x55, 0xec, 0x55, 0x55, 0x50,
    0x3c, 0xaf, 0xa, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x0, 0x5f, 0x0, 0xe, 0x90, 0x5, 0xf2, 0x0,
    0x0, 0x5f, 0x0, 0x5f, 0xe8, 0x4e, 0x90, 0x0,
    0x0, 0x5f, 0x0, 0x0, 0x7f, 0xff, 0x40, 0x0,
    0x6, 0xaf, 0x3, 0x7a, 0xff, 0x8b, 0xfc, 0x50,
    0xe, 0xfa, 0xb, 0xfb, 0x71, 0x0, 0x3b, 0xc0,
    0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+64E6 "擦" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x10, 0x0, 0x0,
    0x0, 0x8c, 0x0, 0x0, 0x1d, 0xb0, 0x0, 0x0,
    0x0, 0x8c, 0x8, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0xa, 0xde, 0xa8, 0xa7, 0x42, 0x65, 0x27, 0xc0,
    0x8, 0xce, 0x83, 0x6f, 0xdd, 0x9d, 0xbd, 0x90,
    0x0, 0x8c, 0x1, 0xdb, 0x5e, 0x4f, 0x4e, 0x50,
    0x0, 0x8c, 0x1d, 0xac, 0xe7, 0xd, 0xca, 0x0,
    0x1, 0xaf, 0xb8, 0xeb, 0xd1, 0x16, 0xf2, 0x0,
    0x4f, 0xfe, 0x40, 0xcd, 0xcf, 0xff, 0xbd, 0x20,
    0x27, 0x9c, 0x2d, 0xc1, 0x0, 0x0, 0x9, 0xf2,
    0x0, 0x8c, 0x18, 0xad, 0xdd, 0xdd, 0xdd, 0x60,
    0x0, 0x8c, 0x0, 0x39, 0x59, 0xf5, 0x65, 0x0,
    0x0, 0x8c, 0x0, 0x7f, 0x36, 0xe2, 0xf7, 0x0,
    0x16, 0xcc, 0xb, 0xf5, 0x4a, 0xe0, 0x5f, 0xb1,
    0xf, 0xf6, 0xa, 0x20, 0xff, 0x80, 0x2, 0xa0,

    /* U+670D "服" */
    0x4, 0xff, 0xff, 0x65, 0xff, 0xff, 0xff, 0x70,
    0x4, 0xf6, 0x6f, 0x65, 0xf6, 0x66, 0x6f, 0x70,
    0x4, 0xf0, 0xe, 0x65, 0xf0, 0x0, 0xf, 0x60,
    0x4, 0xf0, 0xe, 0x65, 0xf0, 0x9b, 0xcf, 0x40,
    0x4, 0xff, 0xff, 0x65, 0xf0, 0x6a, 0xa8, 0x0,
    0x4, 0xf6, 0x5f, 0x65, 0xf7, 0x77, 0x77, 0x40,
    0x4, 0xf0, 0xe, 0x65, 0xff, 0xfe, 0xef, 0xa0,
    0x4, 0xf3, 0x2e, 0x65, 0xf6, 0xe0, 0xd, 0x70,
    0x5, 0xff, 0xff, 0x65, 0xf0, 0xf5, 0x3f, 0x20,
    0x6, 0xe2, 0x2e, 0x65, 0xf0, 0x9d, 0xba, 0x0,
    0x8, 0xc0, 0xe, 0x65, 0xf0, 0x1e, 0xf2, 0x0,
    0xb, 0xa0, 0xe, 0x65, 0xf0, 0x6f, 0xfa, 0x0,
    0x1f, 0x66, 0x9f, 0x55, 0xf9, 0xf7, 0x4f, 0xe2,
    0x2d, 0x16, 0xca, 0x15, 0xf6, 0x50, 0x2, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+672A "未" */
    0x0, 0x0, 0x0, 0xc, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x34, 0x44, 0x4d, 0xb4, 0x44, 0x42, 0x0,
    0x0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0x0, 0x23, 0x33, 0x3d, 0xb3, 0x33, 0x32, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xa0, 0x0, 0x0, 0x0,
    0x5, 0x66, 0x66, 0x6e, 0xc6, 0x66, 0x66, 0x40,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0,
    0x0, 0x0, 0x5, 0xfe, 0xef, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x2e, 0x8c, 0xab, 0xd1, 0x0, 0x0,
    0x0, 0x2, 0xec, 0xc, 0xa1, 0xdd, 0x10, 0x0,
    0x0, 0x5f, 0xd1, 0xc, 0xa0, 0x2e, 0xe4, 0x0,
    0x2b, 0xfc, 0x10, 0xc, 0xa0, 0x2, 0xdf, 0xa1,
    0x2e, 0x70, 0x0, 0xc, 0xa0, 0x0, 0x9, 0xd0,
    0x0, 0x0, 0x0, 0xc, 0xa0, 0x0, 0x0, 0x0,

    /* U+6A21 "模" */
    0x0, 0x3f, 0x10, 0x1, 0xf3, 0x7, 0xe0, 0x0,
    0x0, 0x3f, 0x12, 0xaa, 0xfb, 0xac, 0xfa, 0xa1,
    0x0, 0x4f, 0x22, 0xaa, 0xfb, 0xac, 0xfa, 0xa1,
    0x2f, 0xff, 0xfb, 0x25, 0xc6, 0x48, 0xb4, 0x10,
    0x1, 0x8f, 0x20, 0x7f, 0xcc, 0xcc, 0xcf, 0x50,
    0x0, 0xdf, 0x90, 0x7d, 0x33, 0x33, 0x3e, 0x50,
    0x3, 0xff, 0xf6, 0x7f, 0xbb, 0xbb, 0xbf, 0x50,
    0xb, 0xdf, 0x8b, 0x7d, 0x44, 0x44, 0x4e, 0x50,
    0x3f, 0x7f, 0x11, 0x6d, 0xde, 0xfd, 0xdd, 0x40,
    0x8b, 0x3f, 0x11, 0x44, 0x48, 0xf5, 0x44, 0x40,
    0x12, 0x3f, 0x16, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0x0, 0x3f, 0x10, 0x0, 0x6f, 0xce, 0x30, 0x0,
    0x0, 0x3f, 0x12, 0x6c, 0xf6, 0x8, 0xfa, 0x50,
    0x0, 0x3f, 0x17, 0xd7, 0x10, 0x0, 0x3a, 0xe2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+7A0D "稍" */
    0x0, 0x2, 0x55, 0x2, 0x0, 0xf6, 0x2, 0x20,
    0xc, 0xff, 0xe9, 0x5f, 0x40, 0xf6, 0x1d, 0xb0,
    0x6, 0x6f, 0x40, 0x9, 0xf2, 0xf6, 0xbd, 0x10,
    0x1, 0x3f, 0x51, 0x1, 0x71, 0xf7, 0x53, 0x0,
    0x1f, 0xff, 0xff, 0x4f, 0xff, 0xff, 0xff, 0x70,
    0x5, 0x9f, 0x75, 0x3f, 0x64, 0x44, 0x4f, 0x70,
    0x0, 0xbf, 0xa0, 0x3f, 0x53, 0x33, 0x3f, 0x70,
    0x2, 0xff, 0xf8, 0x3f, 0xff, 0xff, 0xff, 0x70,
    0x9, 0xdf, 0x8f, 0x4f, 0x31, 0x11, 0x1e, 0x70,
    0x2f, 0x7f, 0x42, 0x3f, 0x65, 0x55, 0x5f, 0x70,
    0x3c, 0x2f, 0x40, 0x3f, 0xff, 0xff, 0xff, 0x70,
    0x1, 0x2f, 0x40, 0x3f, 0x20, 0x0, 0xe, 0x70,
    0x0, 0x2f, 0x40, 0x3f, 0x20, 0x5, 0x7f, 0x60,
    0x0, 0x2f, 0x40, 0x3f, 0x20, 0x9, 0xfe, 0x20,
    0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+7EA7 "级" */
    0x0, 0x9, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0x40, 0xef, 0xff, 0xff, 0xf9, 0x0,
    0x0, 0xac, 0x0, 0x5b, 0xd6, 0x66, 0xf8, 0x0,
    0x2, 0xf5, 0x3b, 0x9, 0xb0, 0x3, 0xf2, 0x0,
    0xc, 0xc0, 0xbc, 0xa, 0xd0, 0x9, 0xc0, 0x0,
    0x6f, 0xde, 0xf3, 0xb, 0xf1, 0xf, 0xb6, 0x30,
    0x4a, 0x8e, 0xa0, 0xc, 0xf6, 0x1f, 0xff, 0x70,
    0x0, 0x7e, 0x10, 0xe, 0xfc, 0x0, 0x2f, 0x30,
    0x3, 0xf6, 0x24, 0xf, 0xaf, 0x30, 0x9d, 0x0,
    0x2f, 0xfe, 0xfb, 0x3f, 0x3c, 0xc2, 0xf6, 0x0,
    0xc, 0x85, 0x10, 0x8f, 0x3, 0xfe, 0xc0, 0x0,
    0x0, 0x0, 0x42, 0xea, 0x0, 0xdf, 0x70, 0x0,
    0x28, 0xcf, 0xfc, 0xf3, 0x1c, 0xfc, 0xf7, 0x0,
    0x6e, 0xa6, 0x4f, 0xa5, 0xef, 0x40, 0xaf, 0xb1,
    0x0, 0x0, 0x5e, 0x4f, 0xb2, 0x0, 0x6, 0xf3,
    0x0, 0x0, 0x0, 0x3, 0x0, 0x0, 0x0, 0x10,

    /* U+8BBE "设" */
    0x0, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xfb, 0x0, 0x9, 0xff, 0xff, 0xf1, 0x0,
    0x0, 0x5f, 0xb0, 0x9, 0xe5, 0x59, 0xf1, 0x0,
    0x0, 0x5, 0xe1, 0xb, 0xd0, 0x6, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0x90, 0x6, 0xf8, 0x61,
    0x28, 0x88, 0x14, 0xfc, 0x0, 0x2, 0xef, 0xf2,
    0x3e, 0xef, 0x30, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0x30, 0xff, 0xff, 0xff, 0xff, 0x10,
    0x0, 0x4f, 0x30, 0x6f, 0xa6, 0x66, 0xdf, 0x0,
    0x0, 0x4f, 0x30, 0xa, 0xe0, 0x3, 0xf7, 0x0,
    0x0, 0x4f, 0x31, 0x1, 0xeb, 0x2e, 0xc0, 0x0,
    0x0, 0x4f, 0x6e, 0x50, 0x3f, 0xfd, 0x10, 0x0,
    0x0, 0x4f, 0xfc, 0x12, 0xaf, 0xfe, 0x70, 0x0,
    0x0, 0x8f, 0xa5, 0xbf, 0xe6, 0x19, 0xff, 0x91,
    0x0, 0x17, 0x9, 0xd6, 0x0, 0x0, 0x29, 0xe1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+8BF7 "请" */
    0x1, 0x91, 0x0, 0x0, 0x8, 0xd0, 0x0, 0x0,
    0x1, 0xec, 0x6, 0xee, 0xef, 0xfe, 0xee, 0xd0,
    0x0, 0x4f, 0x81, 0x44, 0x4a, 0xe4, 0x44, 0x30,
    0x0, 0x6, 0x10, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x16, 0x66, 0x2, 0x22, 0x29, 0xd2, 0x22, 0x21,
    0x4f, 0xfe, 0xf, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x8e, 0x0, 0x23, 0x33, 0x33, 0x33, 0x0,
    0x0, 0x7e, 0x0, 0xcf, 0xff, 0xff, 0xff, 0x30,
    0x0, 0x7e, 0x0, 0xc9, 0x11, 0x11, 0x4f, 0x30,
    0x0, 0x7e, 0x0, 0xcf, 0xff, 0xff, 0xff, 0x30,
    0x0, 0x7e, 0x33, 0xc9, 0x11, 0x11, 0x3f, 0x30,
    0x0, 0x7f, 0xf7, 0xcf, 0xff, 0xff, 0xff, 0x30,
    0x0, 0xbf, 0x70, 0xc9, 0x0, 0x13, 0x6f, 0x20,
    0x0, 0x24, 0x0, 0xc9, 0x0, 0x2f, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+8FD4 "返" */
    0x1, 0x80, 0x0, 0x0, 0x12, 0x45, 0x7a, 0x40,
    0x4, 0xf9, 0x0, 0xef, 0xff, 0xff, 0xfe, 0x60,
    0x0, 0x8f, 0x50, 0xeb, 0x65, 0x42, 0x0, 0x0,
    0x0, 0xc, 0x80, 0xe8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xec, 0x99, 0x99, 0x99, 0x10,
    0x1, 0x11, 0x0, 0xef, 0xff, 0xff, 0xff, 0x20,
    0x3f, 0xff, 0x30, 0xf8, 0x11, 0x11, 0xae, 0x0,
    0x15, 0x7f, 0x30, 0xf7, 0xc5, 0x3, 0xf8, 0x0,
    0x0, 0x3f, 0x33, 0xf5, 0xaf, 0x9d, 0xd0, 0x0,
    0x0, 0x3f, 0x37, 0xf1, 0x9, 0xff, 0x40, 0x0,
    0x0, 0x3f, 0x4e, 0xc0, 0x6f, 0xef, 0xe3, 0x0,
    0x0, 0x4f, 0x7d, 0x5d, 0xfc, 0x12, 0xdf, 0x30,
    0x2, 0xef, 0xf6, 0x8, 0x50, 0x0, 0x19, 0x0,
    0x1e, 0xe2, 0xcf, 0xd9, 0x77, 0x66, 0x66, 0x71,
    0xc, 0x20, 0x5, 0xbe, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x10, 0x0,

    /* U+8FDE "连" */
    0x0, 0x0, 0x0, 0x0, 0x37, 0x10, 0x0, 0x0,
    0x4, 0xd2, 0x0, 0x0, 0xbe, 0x0, 0x0, 0x0,
    0x2, 0xed, 0x8, 0xdd, 0xff, 0xdd, 0xdd, 0xd0,
    0x0, 0x3f, 0xa5, 0x9d, 0xf9, 0x99, 0x99, 0x90,
    0x0, 0x6, 0x20, 0x1f, 0x92, 0x82, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0x24, 0xf3, 0x0, 0x0,
    0x3f, 0xff, 0x40, 0xfe, 0xab, 0xfb, 0xaa, 0x60,
    0x17, 0x9f, 0x40, 0xcc, 0xcd, 0xfc, 0xcc, 0x70,
    0x0, 0x3f, 0x40, 0x0, 0x4, 0xf3, 0x0, 0x0,
    0x0, 0x3f, 0x47, 0xaa, 0xab, 0xfb, 0xaa, 0xa1,
    0x0, 0x3f, 0x48, 0xcc, 0xcd, 0xfd, 0xcc, 0xc2,
    0x0, 0x5f, 0x70, 0x0, 0x4, 0xf3, 0x0, 0x0,
    0x3, 0xfe, 0xf7, 0x0, 0x3, 0xf3, 0x0, 0x0,
    0x2e, 0xd1, 0x9f, 0xea, 0x87, 0x76, 0x66, 0x73,
    0xd, 0x20, 0x3, 0x9d, 0xef, 0xff, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+9664 "除" */
    0x7f, 0xff, 0xf2, 0x0, 0x3f, 0xd0, 0x0, 0x0,
    0x7f, 0x69, 0xf1, 0x1, 0xe6, 0xaa, 0x0, 0x0,
    0x7e, 0x8, 0xc0, 0x2d, 0xb0, 0x1e, 0xa0, 0x0,
    0x7e, 0xd, 0x75, 0xfc, 0x0, 0x2, 0xec, 0x20,
    0x7e, 0x1f, 0x5f, 0xcf, 0xff, 0xff, 0xce, 0xc0,
    0x7e, 0x4f, 0x3, 0x7, 0x7e, 0xc7, 0x41, 0x20,
    0x7e, 0xd, 0x60, 0x0, 0xc, 0x90, 0x0, 0x0,
    0x7e, 0x7, 0xc7, 0xff, 0xff, 0xff, 0xff, 0x20,
    0x7e, 0x4, 0xf4, 0x77, 0x7e, 0xc7, 0x77, 0x10,
    0x7e, 0x4, 0xf0, 0x6a, 0xc, 0x94, 0x80, 0x0,
    0x7e, 0x6b, 0xe1, 0xea, 0xc, 0x94, 0xf4, 0x0,
    0x7e, 0x8c, 0x5b, 0xe1, 0xc, 0x90, 0xae, 0x10,
    0x7e, 0x0, 0x7f, 0x34, 0x6e, 0x80, 0x1e, 0x70,
    0x7e, 0x0, 0x4, 0x8, 0xfe, 0x30, 0x2, 0x0,
    0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x49, 0xdc,
    0x0, 0x0, 0x0, 0x0, 0x16, 0xbf, 0xff, 0xff,
    0x0, 0x0, 0x3, 0x8d, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xc7, 0xff,
    0x0, 0x0, 0xff, 0xff, 0xfa, 0x51, 0x0, 0xff,
    0x0, 0x0, 0xff, 0x84, 0x0, 0x0, 0x0, 0xff,
    0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x0, 0x0, 0xff, 0x0, 0x0, 0x2b, 0xee, 0xff,
    0x0, 0x0, 0xff, 0x0, 0x0, 0xdf, 0xff, 0xff,
    0x2b, 0xee, 0xff, 0x0, 0x0, 0xdf, 0xff, 0xfd,
    0xdf, 0xff, 0xff, 0x0, 0x0, 0x2b, 0xff, 0xb2,
    0xdf, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2b, 0xff, 0xb2, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F008 "" */
    0xd0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xd,
    0xff, 0xff, 0xc8, 0x88, 0x88, 0x8c, 0xff, 0xff,
    0xf0, 0xf, 0x80, 0x0, 0x0, 0x8, 0xf0, 0xf,
    0xf0, 0xf, 0x80, 0x0, 0x0, 0x8, 0xf0, 0xf,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xf0, 0xf, 0xec, 0xcc, 0xcc, 0xce, 0xf0, 0xf,
    0xf0, 0xf, 0xec, 0xcc, 0xcc, 0xce, 0xf0, 0xf,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xf0, 0xf, 0x80, 0x0, 0x0, 0x8, 0xf0, 0xf,
    0xf0, 0xf, 0x80, 0x0, 0x0, 0x8, 0xf0, 0xf,
    0xff, 0xff, 0xc8, 0x88, 0x88, 0x8c, 0xff, 0xff,
    0xd0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xd,

    /* U+F00B "" */
    0xdf, 0xff, 0x73, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0xff, 0xff, 0xa5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xdf, 0xff, 0x73, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0x73, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0xff, 0xff, 0xa5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xdf, 0xff, 0x73, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0x73, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0xff, 0xff, 0xa5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xdf, 0xff, 0x73, 0xff, 0xff, 0xff, 0xff, 0xfd,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xb1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xc0,
    0x1b, 0xa0, 0x0, 0x0, 0xb, 0xff, 0xfc, 0x0,
    0xcf, 0xfb, 0x0, 0x0, 0xbf, 0xff, 0xc0, 0x0,
    0xbf, 0xff, 0xb0, 0xb, 0xff, 0xfc, 0x0, 0x0,
    0xc, 0xff, 0xfb, 0xbf, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xb0, 0x0, 0x0, 0x0, 0x0,

    /* U+F00D "" */
    0x3, 0x0, 0x0, 0x0, 0x3, 0x8, 0xfc, 0x10,
    0x0, 0x1c, 0xf8, 0xff, 0xfc, 0x10, 0x1c, 0xff,
    0xf5, 0xff, 0xfc, 0x2c, 0xff, 0xf5, 0x5, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x5, 0xff, 0xff, 0xf5,
    0x0, 0x0, 0x1d, 0xff, 0xfd, 0x10, 0x0, 0x1c,
    0xff, 0xff, 0xfc, 0x10, 0x1c, 0xff, 0xf9, 0xff,
    0xfc, 0x1c, 0xff, 0xf5, 0x5, 0xff, 0xfc, 0xdf,
    0xf5, 0x0, 0x5, 0xff, 0xd1, 0xa4, 0x0, 0x0,
    0x4, 0xa1,

    /* U+F011 "" */
    0x0, 0x0, 0x0, 0x4f, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x10, 0x6f, 0xf1, 0x3, 0x10, 0x0,
    0x0, 0x5f, 0xd0, 0x6f, 0xf1, 0x3f, 0xd1, 0x0,
    0x3, 0xff, 0xf1, 0x6f, 0xf1, 0x5f, 0xfd, 0x0,
    0xd, 0xff, 0x40, 0x6f, 0xf1, 0x9, 0xff, 0x70,
    0x4f, 0xf7, 0x0, 0x6f, 0xf1, 0x0, 0xcf, 0xe0,
    0x9f, 0xf0, 0x0, 0x6f, 0xf1, 0x0, 0x5f, 0xf3,
    0xbf, 0xc0, 0x0, 0x6f, 0xf1, 0x0, 0x2f, 0xf5,
    0xbf, 0xc0, 0x0, 0x4f, 0xe0, 0x0, 0x1f, 0xf6,
    0xaf, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf4,
    0x6f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf0,
    0xf, 0xfe, 0x10, 0x0, 0x0, 0x5, 0xff, 0xa0,
    0x6, 0xff, 0xd3, 0x0, 0x0, 0x7f, 0xff, 0x20,
    0x0, 0x9f, 0xff, 0xda, 0xbe, 0xff, 0xf4, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xff, 0xfd, 0x30, 0x0,
    0x0, 0x0, 0x17, 0xbd, 0xca, 0x50, 0x0, 0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x0, 0x8b, 0xb8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x30, 0x6, 0xff, 0xff, 0x60, 0x3, 0x0,
    0x4, 0xfd, 0xdf, 0xff, 0xff, 0xfd, 0xef, 0x40,
    0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0x4f, 0xff, 0xff, 0xf9, 0x9f, 0xff, 0xff, 0xf4,
    0x8, 0xff, 0xff, 0x20, 0x2, 0xff, 0xff, 0x80,
    0x0, 0xff, 0xf9, 0x0, 0x0, 0x9f, 0xff, 0x0,
    0x0, 0xff, 0xf9, 0x0, 0x0, 0x9f, 0xff, 0x0,
    0x8, 0xff, 0xff, 0x20, 0x2, 0xff, 0xff, 0x80,
    0x4f, 0xff, 0xff, 0xf9, 0x9f, 0xff, 0xff, 0xf4,
    0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0x4, 0xfe, 0xdf, 0xff, 0xff, 0xfd, 0xdf, 0x40,
    0x0, 0x30, 0x6, 0xff, 0xff, 0x60, 0x3, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8b, 0xb8, 0x0, 0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x3, 0xdd, 0x30, 0x3f, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xf5, 0x4f,
    0xf4, 0x0, 0x0, 0x0, 0x9, 0xff, 0x99, 0xff,
    0xbf, 0xf4, 0x0, 0x0, 0x1, 0xbf, 0xf6, 0x22,
    0x6f, 0xff, 0xf4, 0x0, 0x0, 0x2d, 0xfe, 0x35,
    0xff, 0x53, 0xef, 0xf4, 0x0, 0x4, 0xff, 0xc1,
    0x8f, 0xff, 0xf8, 0x1c, 0xfe, 0x40, 0x7f, 0xfa,
    0x1a, 0xff, 0xff, 0xff, 0xa1, 0xaf, 0xf7, 0xcf,
    0x82, 0xdf, 0xff, 0xff, 0xff, 0xfd, 0x28, 0xfc,
    0x14, 0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0,
    0x41, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0xf, 0xff, 0xf9, 0x0, 0x8f,
    0xff, 0xf0, 0x0, 0x0, 0xf, 0xff, 0xf8, 0x0,
    0x8f, 0xff, 0xf0, 0x0, 0x0, 0xf, 0xff, 0xf8,
    0x0, 0x8f, 0xff, 0xf0, 0x0, 0x0, 0xe, 0xff,
    0xf6, 0x0, 0x6f, 0xff, 0xe0, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x0, 0xdf, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xfb, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xfc, 0x1b, 0xb1, 0xcf, 0xff, 0xfd,
    0xff, 0xff, 0xff, 0xc2, 0x2c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xe0, 0xff,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,

    /* U+F01C "" */
    0x0, 0x4, 0xef, 0xff, 0xff, 0xff, 0xfe, 0x40,
    0x0, 0x0, 0x1e, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe1, 0x0, 0x0, 0xaf, 0xb0, 0x0, 0x0, 0x0,
    0xb, 0xfa, 0x0, 0x5, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x1, 0xff, 0x50, 0x1e, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xe1, 0xaf, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xfa, 0xff, 0xff,
    0xff, 0x70, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8,

    /* U+F021 "" */
    0x0, 0x0, 0x6, 0xbd, 0xda, 0x50, 0x2, 0xff,
    0x0, 0x5, 0xef, 0xff, 0xff, 0xfe, 0x42, 0xff,
    0x0, 0x7f, 0xff, 0xa7, 0x7b, 0xff, 0xf9, 0xff,
    0x5, 0xff, 0xc1, 0x0, 0x0, 0x2c, 0xff, 0xff,
    0xe, 0xfc, 0x0, 0x0, 0x2, 0x22, 0xdf, 0xff,
    0x5f, 0xf2, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0x8f, 0xb0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0xb, 0xf8,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x2f, 0xf4,
    0xff, 0xfd, 0x22, 0x20, 0x0, 0x0, 0xcf, 0xe0,
    0xff, 0xff, 0xc2, 0x0, 0x0, 0x2c, 0xff, 0x40,
    0xff, 0x9f, 0xff, 0xb7, 0x6a, 0xff, 0xf7, 0x0,
    0xff, 0x24, 0xdf, 0xff, 0xff, 0xfe, 0x50, 0x0,
    0xff, 0x20, 0x5, 0xac, 0xdb, 0x60, 0x0, 0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8d,
    0x0, 0x0, 0x8, 0xff, 0x0, 0x0, 0x8f, 0xff,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xdf, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x9f, 0xff, 0x0, 0x0, 0x9, 0xff,
    0x0, 0x0, 0x0, 0x8d, 0x0, 0x0, 0x0, 0x0,

    /* U+F027 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8d, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0x0, 0x0,
    0xcf, 0xff, 0xff, 0xff, 0x1, 0x50, 0xff, 0xff,
    0xff, 0xff, 0x6, 0xf7, 0xff, 0xff, 0xff, 0xff,
    0x0, 0xbe, 0xff, 0xff, 0xff, 0xff, 0x0, 0xae,
    0xff, 0xff, 0xff, 0xff, 0x5, 0xf8, 0xdf, 0xff,
    0xff, 0xff, 0x2, 0x60, 0x0, 0x0, 0x9f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9e, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xd2, 0x0, 0x0, 0x0, 0x0, 0x8d, 0x0, 0x0,
    0x3, 0xee, 0x10, 0x0, 0x0, 0x8, 0xff, 0x0,
    0xa, 0xb1, 0x2f, 0xb0, 0x0, 0x0, 0x8f, 0xff,
    0x0, 0x5, 0xfc, 0x7, 0xf4, 0xdf, 0xff, 0xff,
    0xff, 0x2, 0x50, 0x5f, 0x60, 0xf9, 0xff, 0xff,
    0xff, 0xff, 0x6, 0xf7, 0xd, 0xc0, 0xbd, 0xff,
    0xff, 0xff, 0xff, 0x0, 0xae, 0x9, 0xf0, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0x0, 0xae, 0x9, 0xe0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0x6, 0xf7, 0xd,
    0xc0, 0xad, 0xdf, 0xff, 0xff, 0xff, 0x2, 0x50,
    0x5f, 0x60, 0xe9, 0x0, 0x0, 0x9f, 0xff, 0x0,
    0x5, 0xfc, 0x6, 0xf4, 0x0, 0x0, 0x9, 0xff,
    0x0, 0xa, 0xb1, 0x2f, 0xb0, 0x0, 0x0, 0x0,
    0x8d, 0x0, 0x0, 0x2, 0xee, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xd2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0x10, 0x0,

    /* U+F03E "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x20, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x0, 0xc, 0xff, 0xff, 0xee, 0xff, 0xff,
    0xff, 0x20, 0x2f, 0xff, 0xfe, 0x12, 0xef, 0xff,
    0xff, 0xfc, 0xff, 0xff, 0xe1, 0x0, 0x2e, 0xff,
    0xff, 0xfe, 0x4e, 0xfe, 0x10, 0x0, 0x2, 0xff,
    0xff, 0xe1, 0x2, 0xc1, 0x0, 0x0, 0x0, 0xff,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,

    /* U+F043 "" */
    0x0, 0x0, 0x4e, 0x40, 0x0, 0x0, 0x0, 0xb,
    0xfb, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0x90, 0x0, 0x0, 0x2f,
    0xff, 0xff, 0x30, 0x0, 0xc, 0xff, 0xff, 0xfc,
    0x0, 0x7, 0xff, 0xff, 0xff, 0xf8, 0x2, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0x9e, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff,
    0x2f, 0xff, 0xff, 0xff, 0xfe, 0xf2, 0xbf, 0xff,
    0xff, 0xfe, 0x9f, 0xa1, 0xbf, 0xff, 0xff, 0x92,
    0xff, 0xa2, 0x2f, 0xff, 0xf2, 0x4, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0x2, 0x9e, 0xfe, 0x92, 0x0,

    /* U+F048 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x30, 0x0,
    0x1, 0xcc, 0xff, 0x40, 0x0, 0x2d, 0xff, 0xff,
    0x40, 0x3, 0xef, 0xff, 0xff, 0x40, 0x3f, 0xff,
    0xff, 0xff, 0x44, 0xff, 0xff, 0xff, 0xff, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0x45, 0xff, 0xff, 0xff, 0xff,
    0x40, 0x4f, 0xff, 0xff, 0xff, 0x40, 0x3, 0xef,
    0xff, 0xff, 0x40, 0x0, 0x2d, 0xff, 0xff, 0x30,
    0x0, 0x1, 0xcc, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04B "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0x91, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xfd,
    0x40, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xfa,
    0x10, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd5,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb2,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb2, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd5, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0xff, 0xff, 0xff, 0xfa, 0x20,
    0x0, 0x0, 0xff, 0xff, 0xfd, 0x40, 0x0, 0x0,
    0x0, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x8e, 0xa1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0xf8, 0x0, 0x8f, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0xff,
    0xff, 0xff, 0x7f, 0xff, 0xf7, 0x0, 0x7f, 0xff,
    0xf7,

    /* U+F04D "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8,

    /* U+F051 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcc, 0x10, 0x0,
    0x3, 0xff, 0xff, 0xd2, 0x0, 0x4, 0xff, 0xff,
    0xfe, 0x30, 0x4, 0xff, 0xff, 0xff, 0xf4, 0x4,
    0xff, 0xff, 0xff, 0xff, 0x54, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0xff, 0xff, 0xff, 0xff, 0x44, 0xff, 0xff,
    0xff, 0xf3, 0x4, 0xff, 0xff, 0xfe, 0x30, 0x4,
    0xff, 0xff, 0xd2, 0x0, 0x4, 0xff, 0xcc, 0x10,
    0x0, 0x3, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F052 "" */
    0x0, 0x0, 0x0, 0x2d, 0xd2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xef, 0xfe, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x1d, 0xff, 0xff, 0xd1, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,

    /* U+F053 "" */
    0x0, 0x0, 0x0, 0x1a, 0x40, 0x0, 0x0, 0x1,
    0xdf, 0xf0, 0x0, 0x0, 0x1d, 0xff, 0xa0, 0x0,
    0x1, 0xdf, 0xfa, 0x0, 0x0, 0x1d, 0xff, 0xa0,
    0x0, 0x1, 0xdf, 0xfa, 0x0, 0x0, 0xc, 0xff,
    0xa0, 0x0, 0x0, 0xd, 0xff, 0x80, 0x0, 0x0,
    0x1, 0xdf, 0xf8, 0x0, 0x0, 0x0, 0x1d, 0xff,
    0x80, 0x0, 0x0, 0x1, 0xdf, 0xf8, 0x0, 0x0,
    0x0, 0x1d, 0xff, 0x80, 0x0, 0x0, 0x1, 0xdf,
    0xf0, 0x0, 0x0, 0x0, 0x1b, 0x50,

    /* U+F054 "" */
    0x4, 0xa1, 0x0, 0x0, 0x0, 0xf, 0xfc, 0x10,
    0x0, 0x0, 0xa, 0xff, 0xc1, 0x0, 0x0, 0x0,
    0xaf, 0xfc, 0x10, 0x0, 0x0, 0xa, 0xff, 0xc1,
    0x0, 0x0, 0x0, 0xaf, 0xfc, 0x10, 0x0, 0x0,
    0xa, 0xff, 0xc0, 0x0, 0x0, 0x8, 0xff, 0xd0,
    0x0, 0x0, 0x8f, 0xfd, 0x10, 0x0, 0x8, 0xff,
    0xd1, 0x0, 0x0, 0x8f, 0xfd, 0x10, 0x0, 0x8,
    0xff, 0xd1, 0x0, 0x0, 0xf, 0xfd, 0x10, 0x0,
    0x0, 0x5, 0xb1, 0x0, 0x0, 0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x4, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0x80, 0x0, 0x0, 0x48, 0x88, 0x8c, 0xff, 0xc8,
    0x88, 0x84, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x48, 0x88, 0x8c, 0xff, 0xc8, 0x88, 0x84, 0x0,
    0x0, 0x8, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x40,
    0x0, 0x0,

    /* U+F068 "" */
    0x14, 0x44, 0x44, 0x44, 0x44, 0x44, 0x41, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x7b, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xb7,

    /* U+F06E "" */
    0x0, 0x0, 0x5, 0xad, 0xff, 0xda, 0x50, 0x0,
    0x0, 0x0, 0x4, 0xdf, 0xfc, 0x88, 0xcf, 0xfd,
    0x40, 0x0, 0x0, 0x7f, 0xfe, 0x40, 0x0, 0x4,
    0xef, 0xf7, 0x0, 0x7, 0xff, 0xf4, 0x0, 0x9e,
    0x80, 0x4f, 0xff, 0x70, 0x4f, 0xff, 0xc0, 0x0,
    0xaf, 0xf8, 0xc, 0xff, 0xf4, 0xdf, 0xff, 0x80,
    0x9a, 0xff, 0xfe, 0x8, 0xff, 0xfd, 0xdf, 0xff,
    0x80, 0xef, 0xff, 0xfe, 0x8, 0xff, 0xfd, 0x4f,
    0xff, 0xc0, 0x8f, 0xff, 0xf8, 0xc, 0xff, 0xf4,
    0x7, 0xff, 0xf4, 0x8, 0xee, 0x80, 0x4f, 0xff,
    0x70, 0x0, 0x7f, 0xfe, 0x40, 0x0, 0x4, 0xef,
    0xf8, 0x0, 0x0, 0x4, 0xdf, 0xfc, 0x88, 0xcf,
    0xfd, 0x40, 0x0, 0x0, 0x0, 0x5, 0xad, 0xff,
    0xda, 0x50, 0x0, 0x0,

    /* U+F070 "" */
    0x8c, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xe4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1b, 0xff, 0x80, 0x49,
    0xdf, 0xfd, 0xa5, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0xff, 0xd8, 0x8c, 0xff, 0xd4, 0x0, 0x0,
    0x0, 0x4, 0xef, 0xf8, 0x0, 0x0, 0x4e, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x1c, 0xff, 0x69, 0xe8,
    0x4, 0xff, 0xf7, 0x0, 0x4, 0xe3, 0x0, 0x9f,
    0xfe, 0xff, 0x80, 0xcf, 0xff, 0x40, 0xd, 0xff,
    0x70, 0x5, 0xff, 0xff, 0xe0, 0x8f, 0xff, 0xd0,
    0xd, 0xff, 0xf7, 0x0, 0x2d, 0xff, 0xe0, 0x8f,
    0xff, 0xd0, 0x4, 0xff, 0xfc, 0x0, 0x0, 0xaf,
    0xf8, 0xcf, 0xff, 0x30, 0x0, 0x7f, 0xff, 0x40,
    0x0, 0x6, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x8,
    0xff, 0xf4, 0x0, 0x0, 0x3e, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x4d, 0xff, 0xc8, 0x82, 0x1, 0xbf,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x5a, 0xdf, 0xfc,
    0x10, 0x8, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4e, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xc8,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x0, 0x2d, 0xd2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xd8, 0x8d,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xa0, 0xa, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xb0, 0xb, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xc0, 0xc, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xd0, 0xd,
    0xff, 0xff, 0x50, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xf9, 0x9f, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x8f,
    0xff, 0xff, 0xe2, 0x2e, 0xff, 0xff, 0xf8, 0x0,
    0x2, 0xff, 0xff, 0xff, 0x90, 0x9, 0xff, 0xff,
    0xff, 0x10, 0xa, 0xff, 0xff, 0xff, 0xe3, 0x3e,
    0xff, 0xff, 0xff, 0xa0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x80,
    0xff, 0xff, 0x70, 0x0, 0x7, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xf6, 0x0, 0x6f, 0xff, 0xff, 0xfd,
    0x78, 0x8e, 0xff, 0x15, 0xff, 0xe8, 0xff, 0xe2,
    0x0, 0x2, 0xe5, 0x4f, 0xfe, 0x20, 0xfe, 0x20,
    0x0, 0x0, 0x13, 0xff, 0xf3, 0x0, 0x52, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0x31, 0x0, 0x52, 0x0,
    0x0, 0x2, 0xef, 0xf4, 0x5e, 0x20, 0xfe, 0x20,
    0x78, 0x8e, 0xff, 0x51, 0xff, 0xe8, 0xff, 0xe2,
    0xff, 0xff, 0xf6, 0x0, 0x6f, 0xff, 0xff, 0xfd,
    0xff, 0xff, 0x70, 0x0, 0x7, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F077 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xdd, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x1d, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x1, 0xdf,
    0xff, 0xfd, 0x10, 0x0, 0x0, 0x1d, 0xff, 0x99,
    0xff, 0xd1, 0x0, 0x1, 0xdf, 0xf9, 0x0, 0x9f,
    0xfd, 0x10, 0x1d, 0xff, 0x90, 0x0, 0x9, 0xff,
    0xd1, 0xbf, 0xf9, 0x0, 0x0, 0x0, 0x9f, 0xfb,
    0x5f, 0x90, 0x0, 0x0, 0x0, 0x9, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F078 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0x90, 0x0, 0x0, 0x0, 0x9, 0xf5, 0xbf, 0xf9,
    0x0, 0x0, 0x0, 0x9f, 0xfb, 0x1d, 0xff, 0x90,
    0x0, 0x9, 0xff, 0xd1, 0x1, 0xdf, 0xf9, 0x0,
    0x9f, 0xfd, 0x10, 0x0, 0x1d, 0xff, 0x99, 0xff,
    0xd1, 0x0, 0x0, 0x1, 0xdf, 0xff, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0x1d, 0xff, 0xd1, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xdd, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F079 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1d, 0xd1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xdf, 0xfd, 0x10,
    0xef, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x1d, 0xff,
    0xff, 0xd1, 0xaf, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0xcf, 0xcf, 0xfc, 0xfc, 0x0, 0x0, 0x0, 0xf,
    0xf0, 0x0, 0x6b, 0x1f, 0xf1, 0xb6, 0x0, 0x0,
    0x0, 0xf, 0xf0, 0x0, 0x0, 0xf, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf0, 0x0, 0x0, 0xf,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x0,
    0x0, 0xf, 0xf0, 0x0, 0x0, 0x0, 0x6b, 0x1f,
    0xf1, 0xb6, 0x0, 0xf, 0xf0, 0x0, 0x0, 0x0,
    0xcf, 0xcf, 0xfc, 0xfc, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xfa, 0x1d, 0xff, 0xff, 0xd1, 0x0, 0xd,
    0xff, 0xff, 0xff, 0xfe, 0x1, 0xdf, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1d,
    0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F07B "" */
    0x8f, 0xff, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xfd, 0x10, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,

    /* U+F093 "" */
    0x0, 0x0, 0x0, 0xb, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xf0, 0xdf, 0xfd, 0xf, 0xff, 0xfd,
    0xff, 0xff, 0xf9, 0x0, 0x0, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xe0, 0xff,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xea,
    0x62, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xef, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0x30, 0x0, 0x0, 0x2,
    0x0, 0x0, 0x4f, 0xff, 0x90, 0x0, 0x2, 0x8f,
    0xf3, 0x0, 0x6f, 0xff, 0xd0, 0x0, 0xa, 0xff,
    0xff, 0xe4, 0xbf, 0xff, 0xd1, 0x0, 0x0, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xd1, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xff, 0xfb, 0x30, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xdb, 0x72, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F0C4 "" */
    0x8, 0xee, 0x80, 0x0, 0x0, 0x6, 0x61, 0x8,
    0xff, 0xff, 0x80, 0x0, 0x2d, 0xff, 0xd0, 0xef,
    0x33, 0xfe, 0x0, 0x2e, 0xff, 0xf3, 0xe, 0xf3,
    0x3f, 0xe0, 0x2e, 0xff, 0xf3, 0x0, 0x8f, 0xff,
    0xff, 0x6e, 0xff, 0xf3, 0x0, 0x0, 0x8e, 0xff,
    0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x2, 0xef,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xff,
    0xff, 0x30, 0x0, 0x0, 0x8, 0xef, 0xff, 0xff,
    0xff, 0x30, 0x0, 0x8, 0xff, 0xff, 0xf6, 0xef,
    0xff, 0x30, 0x0, 0xef, 0x33, 0xfe, 0x2, 0xef,
    0xff, 0x30, 0xe, 0xf3, 0x3f, 0xe0, 0x2, 0xef,
    0xff, 0x30, 0x8f, 0xff, 0xf8, 0x0, 0x2, 0xdf,
    0xfd, 0x0, 0x8e, 0xe8, 0x0, 0x0, 0x0, 0x66,
    0x10,

    /* U+F0C5 "" */
    0x0, 0x0, 0xdf, 0xff, 0xff, 0xd, 0x10, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xf, 0xd1, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xf, 0xfd, 0xdf, 0xf0, 0xff,
    0xff, 0xff, 0x20, 0x0, 0xff, 0xf0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xdf, 0xff,
    0xff, 0xff, 0xfd, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x0, 0xdf, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0,

    /* U+F0C7 "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xc1, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x10, 0xff, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xd1, 0xff, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xfc, 0xff, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x11, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x11, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8,

    /* U+F0C9 "" */
    0x12, 0x22, 0x22, 0x22, 0x22, 0x22, 0x21, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x12, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x21, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x12, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x21, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x12, 0x22, 0x22, 0x22, 0x22, 0x22, 0x21, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x12, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x21, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x12, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x21,

    /* U+F0E0 "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90,
    0xd2, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x2d,
    0xff, 0x62, 0xcf, 0xff, 0xff, 0xfc, 0x26, 0xff,
    0xff, 0xfa, 0x18, 0xff, 0xff, 0x81, 0xaf, 0xff,
    0xff, 0xff, 0xe3, 0x4d, 0xd4, 0x3e, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x81, 0x18, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,

    /* U+F0E7 "" */
    0x0, 0xdf, 0xff, 0xfd, 0x0, 0x0, 0x1, 0xff,
    0xff, 0xfc, 0x0, 0x0, 0x3, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xf2, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xd0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xa0, 0xe, 0xff, 0xff, 0xff, 0xff, 0x20,
    0xd, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xe0, 0x0, 0x0, 0x0, 0xe, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x2f, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xa0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x10,
    0x0, 0x0, 0x0, 0x0, 0xd7, 0x0, 0x0, 0x0,

    /* U+F0EA "" */
    0x0, 0x4, 0xde, 0x40, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0x99, 0xff, 0xfd, 0x0, 0x0, 0xff, 0xff,
    0x99, 0xff, 0xff, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xd, 0xff, 0xff,
    0xd, 0x10, 0xff, 0xff, 0xf, 0xff, 0xff, 0xf,
    0xd1, 0xff, 0xff, 0xf, 0xff, 0xff, 0xf, 0xfd,
    0xff, 0xff, 0xf, 0xff, 0xff, 0x20, 0x0, 0xff,
    0xff, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xdf, 0xff, 0xf, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x0, 0xd, 0xff, 0xff, 0xff, 0xfd,

    /* U+F0F3 "" */
    0x0, 0x0, 0x0, 0xcc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0x30, 0x0, 0x0, 0x0, 0x1,
    0xbf, 0xff, 0xfc, 0x20, 0x0, 0x0, 0x1e, 0xff,
    0xff, 0xff, 0xe1, 0x0, 0x0, 0x9f, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xfd, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x3, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0x8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x1e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe1, 0xcf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xee, 0x40, 0x0, 0x0,

    /* U+F11C "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x0, 0xf0, 0xf, 0x0, 0xf0,
    0xf, 0x0, 0xff, 0xff, 0x0, 0xf0, 0xf, 0x0,
    0xf0, 0xf, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x8,
    0x80, 0x88, 0x8, 0x80, 0x8f, 0xff, 0xff, 0xf8,
    0x8, 0x80, 0x88, 0x8, 0x80, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0xf0, 0x0, 0x0, 0x0, 0xf, 0x0,
    0xff, 0xff, 0x0, 0xf0, 0x0, 0x0, 0x0, 0xf,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xaf, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xcf, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xdf, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x17,
    0xef, 0xff, 0xff, 0xff, 0x30, 0x0, 0x0, 0x18,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x2a,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F15B "" */
    0xdf, 0xff, 0xff, 0xf0, 0xd1, 0x0, 0xff, 0xff,
    0xff, 0xf0, 0xfd, 0x10, 0xff, 0xff, 0xff, 0xf0,
    0xff, 0xd1, 0xff, 0xff, 0xff, 0xf0, 0xff, 0xfd,
    0xff, 0xff, 0xff, 0xf2, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xfd,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0x9c, 0xef, 0xfe,
    0xc9, 0x40, 0x0, 0x0, 0x0, 0x7, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x70, 0x0, 0x4, 0xdf,
    0xff, 0xfc, 0xa8, 0x8a, 0xcf, 0xff, 0xfd, 0x40,
    0x6f, 0xff, 0xd5, 0x0, 0x0, 0x0, 0x0, 0x5d,
    0xff, 0xf6, 0xcf, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xfc, 0x1a, 0x30, 0x0, 0x5a,
    0xdf, 0xfd, 0xa5, 0x0, 0x3, 0xa1, 0x0, 0x0,
    0x4d, 0xff, 0xff, 0xff, 0xff, 0xd4, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xfe, 0xa8, 0x8a, 0xef, 0xff,
    0x50, 0x0, 0x0, 0x1, 0xdf, 0x70, 0x0, 0x0,
    0x7, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x12, 0x0,
    0x0, 0x0, 0x0, 0x21, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4e, 0xe4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4e, 0xe4, 0x0, 0x0, 0x0, 0x0,

    /* U+F240 "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfd, 0xff, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf, 0xff,
    0xff, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0xff, 0xff, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0xff, 0xff, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80,

    /* U+F241 "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfd, 0xff, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0xf, 0xff,
    0xff, 0xf, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0xff, 0xff, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0xff, 0xff, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0xf, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80,

    /* U+F242 "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfd, 0xff, 0xf,
    0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xf, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xf, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xf, 0xff, 0xff,
    0xff, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80,

    /* U+F243 "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfd, 0xff, 0xf,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xf, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xf, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xf, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80,

    /* U+F244 "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfd, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xcf, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb9, 0x29, 0xfe, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0x10, 0x2,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xdf, 0x80, 0xa,
    0x90, 0x0, 0x0, 0x0, 0x3, 0x70, 0x0, 0xdf,
    0xff, 0x77, 0xf7, 0x55, 0x55, 0x55, 0x55, 0x8f,
    0xd3, 0xf, 0xff, 0xfd, 0xcc, 0xdf, 0xdc, 0xcc,
    0xcc, 0xcd, 0xff, 0xb0, 0x8f, 0xfe, 0x10, 0x0,
    0xaa, 0x0, 0x0, 0x0, 0x4d, 0x40, 0x0, 0x46,
    0x10, 0x0, 0x1, 0xf2, 0x2, 0x33, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xb1, 0xcf,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x22,
    0x0, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x18, 0xdf, 0xfd, 0x92, 0x0, 0x2, 0xef,
    0xfb, 0xef, 0xff, 0x40, 0xd, 0xff, 0xfa, 0x2e,
    0xff, 0xe0, 0x4f, 0xff, 0xfa, 0x3, 0xff, 0xf5,
    0x9f, 0xfa, 0xfa, 0x35, 0x4f, 0xfa, 0xcf, 0xc0,
    0x8a, 0x3d, 0xb, 0xfd, 0xef, 0xfb, 0x3, 0x11,
    0x8f, 0xfe, 0xff, 0xff, 0xb0, 0x6, 0xff, 0xff,
    0xff, 0xff, 0xd1, 0x8, 0xff, 0xff, 0xef, 0xfd,
    0x11, 0x10, 0x9f, 0xff, 0xdf, 0xd1, 0x59, 0x3b,
    0xb, 0xfd, 0xaf, 0xd6, 0xfa, 0x37, 0x1d, 0xfb,
    0x5f, 0xff, 0xfa, 0x1, 0xdf, 0xf7, 0xd, 0xff,
    0xfa, 0x1d, 0xff, 0xf1, 0x3, 0xef, 0xfc, 0xdf,
    0xff, 0x50, 0x0, 0x18, 0xdf, 0xfe, 0xa3, 0x0,

    /* U+F2ED "" */
    0x0, 0x0, 0x7f, 0xff, 0xf7, 0x0, 0x0, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0xf, 0xf9, 0x9f, 0x99, 0xf9, 0x9f,
    0xf0, 0xf, 0xf8, 0x8f, 0x88, 0xf8, 0x8f, 0xf0,
    0xf, 0xf8, 0x8f, 0x88, 0xf8, 0x8f, 0xf0, 0xf,
    0xf8, 0x8f, 0x88, 0xf8, 0x8f, 0xf0, 0xf, 0xf8,
    0x8f, 0x88, 0xf8, 0x8f, 0xf0, 0xf, 0xf8, 0x8f,
    0x88, 0xf8, 0x8f, 0xf0, 0xf, 0xf8, 0x8f, 0x88,
    0xf8, 0x8f, 0xf0, 0xf, 0xf9, 0x9f, 0x99, 0xf9,
    0x9f, 0xf0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x7a, 0x1d,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xfa,
    0x1d, 0xff, 0x70, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xfa, 0x1d, 0x70, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xde, 0xdb, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F55A "" */
    0x0, 0x0, 0x1b, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe4, 0x0, 0x1, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0x1d, 0xff, 0xff,
    0xfa, 0xef, 0xfe, 0xaf, 0xff, 0xff, 0x1, 0xdf,
    0xff, 0xff, 0xa0, 0x2e, 0xe2, 0xa, 0xff, 0xff,
    0x1d, 0xff, 0xff, 0xff, 0xe2, 0x2, 0x20, 0x2e,
    0xff, 0xff, 0xcf, 0xff, 0xff, 0xff, 0xfe, 0x20,
    0x2, 0xef, 0xff, 0xff, 0xcf, 0xff, 0xff, 0xff,
    0xfe, 0x20, 0x2, 0xef, 0xff, 0xff, 0x1d, 0xff,
    0xff, 0xff, 0xe2, 0x2, 0x20, 0x2e, 0xff, 0xff,
    0x1, 0xdf, 0xff, 0xff, 0xa0, 0x2e, 0xe2, 0xa,
    0xff, 0xff, 0x0, 0x1d, 0xff, 0xff, 0xfa, 0xef,
    0xfe, 0xaf, 0xff, 0xff, 0x0, 0x1, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x1b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe4,

    /* U+F7C2 "" */
    0x0, 0x7, 0xff, 0xff, 0xff, 0xe4, 0x0, 0x7f,
    0xff, 0xff, 0xff, 0xfe, 0x7, 0xf8, 0xf, 0xb,
    0x40, 0xff, 0x7f, 0xf8, 0xf, 0xb, 0x40, 0xff,
    0xff, 0xf8, 0xf, 0xb, 0x40, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x4e, 0xff, 0xff, 0xff, 0xff, 0xe4,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xe0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x2,
    0xef, 0x10, 0x0, 0xbf, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf1, 0x0, 0xcf, 0xf1, 0x0, 0x0, 0x0,
    0x7, 0xff, 0x11, 0xcf, 0xff, 0x77, 0x77, 0x77,
    0x77, 0xbf, 0xf1, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x17, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x7, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 69, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 63, .box_w = 4, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 26, .adv_w = 96, .box_w = 6, .box_h = 5, .ofs_x = 0, .ofs_y = 9},
    {.bitmap_index = 41, .adv_w = 169, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 107, .adv_w = 148, .box_w = 9, .box_h = 17, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 184, .adv_w = 204, .box_w = 13, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 262, .adv_w = 184, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 340, .adv_w = 51, .box_w = 3, .box_h = 5, .ofs_x = 0, .ofs_y = 9},
    {.bitmap_index = 348, .adv_w = 91, .box_w = 5, .box_h = 16, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 388, .adv_w = 91, .box_w = 5, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 428, .adv_w = 113, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 453, .adv_w = 148, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 494, .adv_w = 65, .box_w = 4, .box_h = 6, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 506, .adv_w = 125, .box_w = 7, .box_h = 3, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 517, .adv_w = 62, .box_w = 3, .box_h = 3, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 522, .adv_w = 103, .box_w = 7, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 564, .adv_w = 148, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 618, .adv_w = 148, .box_w = 6, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 654, .adv_w = 148, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 708, .adv_w = 148, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 762, .adv_w = 148, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 822, .adv_w = 148, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 870, .adv_w = 148, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 924, .adv_w = 148, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 978, .adv_w = 148, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1032, .adv_w = 148, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1086, .adv_w = 68, .box_w = 3, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1100, .adv_w = 70, .box_w = 3, .box_h = 12, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 1118, .adv_w = 148, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 1159, .adv_w = 148, .box_w = 9, .box_h = 5, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 1182, .adv_w = 148, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 1223, .adv_w = 115, .box_w = 9, .box_h = 13, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 1282, .adv_w = 251, .box_w = 16, .box_h = 17, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 1418, .adv_w = 175, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1484, .adv_w = 169, .box_w = 9, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1538, .adv_w = 169, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1604, .adv_w = 185, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1664, .adv_w = 154, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1712, .adv_w = 146, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1760, .adv_w = 181, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1826, .adv_w = 193, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1886, .adv_w = 70, .box_w = 3, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1904, .adv_w = 123, .box_w = 7, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1946, .adv_w = 179, .box_w = 11, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2012, .adv_w = 146, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2060, .adv_w = 222, .box_w = 12, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2132, .adv_w = 191, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2192, .adv_w = 197, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2264, .adv_w = 156, .box_w = 9, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2318, .adv_w = 197, .box_w = 13, .box_h = 15, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2416, .adv_w = 168, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2476, .adv_w = 148, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2530, .adv_w = 149, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2590, .adv_w = 189, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2650, .adv_w = 173, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2716, .adv_w = 251, .box_w = 16, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2812, .adv_w = 173, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2878, .adv_w = 163, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2944, .adv_w = 149, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2998, .adv_w = 91, .box_w = 5, .box_h = 16, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 3038, .adv_w = 103, .box_w = 7, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3080, .adv_w = 91, .box_w = 5, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3120, .adv_w = 126, .box_w = 8, .box_h = 7, .ofs_x = 0, .ofs_y = 6},
    {.bitmap_index = 3148, .adv_w = 112, .box_w = 7, .box_h = 3, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 3159, .adv_w = 81, .box_w = 5, .box_h = 3, .ofs_x = 0, .ofs_y = 10},
    {.bitmap_index = 3167, .adv_w = 141, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3203, .adv_w = 157, .box_w = 9, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3262, .adv_w = 127, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3298, .adv_w = 157, .box_w = 9, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3357, .adv_w = 141, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3398, .adv_w = 91, .box_w = 7, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3444, .adv_w = 157, .box_w = 9, .box_h = 13, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 3503, .adv_w = 151, .box_w = 8, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3555, .adv_w = 64, .box_w = 4, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3579, .adv_w = 64, .box_w = 7, .box_h = 16, .ofs_x = -3, .ofs_y = -4},
    {.bitmap_index = 3635, .adv_w = 139, .box_w = 8, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3687, .adv_w = 64, .box_w = 2, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3700, .adv_w = 220, .box_w = 12, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3754, .adv_w = 151, .box_w = 8, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3790, .adv_w = 152, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3831, .adv_w = 157, .box_w = 9, .box_h = 13, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 3890, .adv_w = 157, .box_w = 9, .box_h = 13, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 3949, .adv_w = 100, .box_w = 6, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3976, .adv_w = 118, .box_w = 7, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4008, .adv_w = 99, .box_w = 7, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4050, .adv_w = 151, .box_w = 8, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4086, .adv_w = 136, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4127, .adv_w = 203, .box_w = 13, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4186, .adv_w = 131, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4227, .adv_w = 137, .box_w = 9, .box_h = 13, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 4286, .adv_w = 122, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4322, .adv_w = 98, .box_w = 6, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4370, .adv_w = 51, .box_w = 3, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4394, .adv_w = 98, .box_w = 6, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4442, .adv_w = 148, .box_w = 9, .box_h = 4, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 4460, .adv_w = 256, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4573, .adv_w = 256, .box_w = 16, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4693, .adv_w = 256, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4806, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4934, .adv_w = 256, .box_w = 16, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5054, .adv_w = 256, .box_w = 16, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5174, .adv_w = 256, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5287, .adv_w = 256, .box_w = 14, .box_h = 14, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 5385, .adv_w = 256, .box_w = 16, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5505, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5633, .adv_w = 256, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5753, .adv_w = 256, .box_w = 16, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5873, .adv_w = 256, .box_w = 16, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5985, .adv_w = 256, .box_w = 16, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6105, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6233, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6361, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6489, .adv_w = 256, .box_w = 16, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6609, .adv_w = 256, .box_w = 16, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6729, .adv_w = 256, .box_w = 16, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6841, .adv_w = 256, .box_w = 16, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6961, .adv_w = 256, .box_w = 16, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7081, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7209, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7337, .adv_w = 256, .box_w = 16, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7457, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7585, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7713, .adv_w = 256, .box_w = 16, .box_h = 15, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 7833, .adv_w = 256, .box_w = 16, .box_h = 17, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7969, .adv_w = 256, .box_w = 16, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8065, .adv_w = 256, .box_w = 16, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 8177, .adv_w = 256, .box_w = 16, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8273, .adv_w = 176, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8339, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8467, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8595, .adv_w = 288, .box_w = 18, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 8721, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8849, .adv_w = 288, .box_w = 18, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8957, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9085, .adv_w = 128, .box_w = 8, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 9141, .adv_w = 192, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 9225, .adv_w = 288, .box_w = 18, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9369, .adv_w = 256, .box_w = 16, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9465, .adv_w = 176, .box_w = 11, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9553, .adv_w = 224, .box_w = 10, .box_h = 16, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 9633, .adv_w = 224, .box_w = 14, .box_h = 18, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 9759, .adv_w = 224, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 9864, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 9962, .adv_w = 224, .box_w = 10, .box_h = 16, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 10042, .adv_w = 224, .box_w = 16, .box_h = 14, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 10154, .adv_w = 160, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 10224, .adv_w = 160, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 10294, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 10392, .adv_w = 224, .box_w = 14, .box_h = 4, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 10420, .adv_w = 288, .box_w = 18, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10528, .adv_w = 320, .box_w = 20, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 10688, .adv_w = 288, .box_w = 20, .box_h = 16, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 10848, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 10976, .adv_w = 224, .box_w = 14, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 11046, .adv_w = 224, .box_w = 14, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 11116, .adv_w = 320, .box_w = 20, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 11256, .adv_w = 256, .box_w = 16, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11352, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 11480, .adv_w = 256, .box_w = 17, .box_h = 17, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 11625, .adv_w = 224, .box_w = 15, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 11730, .adv_w = 224, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 11842, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 11940, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 12038, .adv_w = 256, .box_w = 16, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12134, .adv_w = 160, .box_w = 12, .box_h = 16, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 12230, .adv_w = 224, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 12342, .adv_w = 224, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 12454, .adv_w = 288, .box_w = 18, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12562, .adv_w = 256, .box_w = 18, .box_h = 18, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 12724, .adv_w = 192, .box_w = 12, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 12820, .adv_w = 320, .box_w = 20, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 12970, .adv_w = 320, .box_w = 20, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 13070, .adv_w = 320, .box_w = 20, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 13170, .adv_w = 320, .box_w = 20, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 13270, .adv_w = 320, .box_w = 20, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 13370, .adv_w = 320, .box_w = 20, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 13470, .adv_w = 320, .box_w = 21, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 13617, .adv_w = 224, .box_w = 12, .box_h = 16, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 13713, .adv_w = 224, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 13825, .adv_w = 256, .box_w = 17, .box_h = 17, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 13970, .adv_w = 320, .box_w = 20, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 14090, .adv_w = 192, .box_w = 12, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 14186, .adv_w = 258, .box_w = 17, .box_h = 11, .ofs_x = 0, .ofs_y = 1}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x1a7, 0x3cd, 0x467, 0x50d, 0x5d4, 0x5f5, 0x8a4,
    0xacd, 0xb91, 0xd52, 0x10c6, 0x10d5, 0x1235, 0x13d6, 0x1528,
    0x156b, 0x16ac, 0x18d3, 0x18f0, 0x1be7, 0x2bd3, 0x306d, 0x3d84,
    0x3dbd, 0x419a, 0x41a4, 0x482a, 0xa1c7, 0xa1ce, 0xa1d1, 0xa1d2,
    0xa1d3, 0xa1d7, 0xa1d9, 0xa1db, 0xa1df, 0xa1e2, 0xa1e7, 0xa1ec,
    0xa1ed, 0xa1ee, 0xa204, 0xa209, 0xa20e, 0xa211, 0xa212, 0xa213,
    0xa217, 0xa218, 0xa219, 0xa21a, 0xa22d, 0xa22e, 0xa234, 0xa236,
    0xa237, 0xa23a, 0xa23d, 0xa23e, 0xa23f, 0xa241, 0xa259, 0xa25b,
    0xa28a, 0xa28b, 0xa28d, 0xa28f, 0xa2a6, 0xa2ad, 0xa2b0, 0xa2b9,
    0xa2e2, 0xa2ea, 0xa321, 0xa3b1, 0xa406, 0xa407, 0xa408, 0xa409,
    0xa40a, 0xa44d, 0xa459, 0xa4b3, 0xa4ca, 0xa720, 0xa988, 0xaa68
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 20026, .range_length = 43625, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 88, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 0, 0, 1, 0, 0, 0, 0,
    1, 2, 3, 0, 4, 0, 4, 0,
    5, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 4,
    6, 7, 8, 9, 10, 7, 11, 12,
    13, 14, 14, 15, 16, 17, 14, 14,
    7, 18, 0, 19, 20, 21, 15, 5,
    22, 23, 24, 25, 2, 8, 3, 0,
    0, 0, 26, 27, 28, 29, 30, 31,
    32, 26, 0, 33, 34, 29, 26, 26,
    27, 27, 0, 35, 36, 37, 32, 38,
    38, 39, 38, 40, 2, 0, 3, 4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 1, 0, 2, 0, 0, 0, 0,
    2, 0, 3, 0, 4, 5, 4, 5,
    6, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 4, 0, 0,
    7, 8, 6, 9, 8, 9, 9, 9,
    8, 9, 9, 10, 9, 9, 9, 9,
    8, 9, 8, 9, 11, 12, 13, 14,
    15, 16, 17, 18, 0, 14, 3, 0,
    5, 0, 19, 20, 21, 21, 21, 22,
    21, 20, 0, 23, 20, 20, 24, 24,
    21, 0, 21, 24, 25, 26, 27, 28,
    28, 29, 30, 31, 0, 0, 3, 4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    0, 0, 0, 0, 0, 0, 4, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 5, 0, 5, 5,
    3, 0, 4, 0, 0, 18, 0, 0,
    5, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 6, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -14, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -10, 5, 5, -11,
    -36, -24, 6, -9, 0, -31, -3, 5,
    0, 0, 0, 0, 0, 0, -19, 0,
    -19, -6, 0, -12, -15, -2, -12, -12,
    -14, -12, -14, 0, 0, 0, -8, -26,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -6, 0, 0, -11, -9,
    0, 0, 0, -9, 0, -8, 0, -9,
    -5, -9, -14, -6, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -10, -30, 0, -16, 7, 0, -17,
    -9, 0, 0, 0, -22, -4, -24, -18,
    0, -28, 5, 0, 0, -3, 0, 0,
    0, 0, 0, 0, -11, 0, -11, 0,
    0, -3, 0, 0, 0, -4, 0, 0,
    0, 4, 0, -9, 0, -12, -4, 0,
    -15, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    3, 0, -8, 6, 0, 8, -4, 0,
    0, 0, 1, 0, -1, 0, 0, -1,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -6, 0, -6, 0, 0, 0,
    0, 0, 3, 0, 3, -3, 0, 3,
    0, 0, 0, -3, 0, 0, -3, 0,
    -3, 0, -3, -4, 0, 0, -2, -3,
    -2, -5, -2, -5, 0, -3, 7, 0,
    2, -34, -15, 10, -1, 0, -36, 0,
    6, 0, 0, 0, 0, 0, 0, -10,
    0, -7, -2, 0, -5, 0, -3, 0,
    -6, -9, -6, -6, 0, 0, 0, 0,
    5, 0, 3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 2, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -8, -4,
    0, 0, 0, -8, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -10, 0, 0, -28, 5, 0, -2,
    -15, -4, 0, -4, 0, -7, 0, 0,
    0, 0, 0, -7, 0, -9, -10, 0,
    -4, -4, -10, -11, -17, -9, -17, 0,
    -13, -26, 0, -23, 7, 0, -18, -12,
    0, 4, -2, -34, -11, -38, -28, 0,
    -46, 0, -2, 0, -5, -5, 0, -1,
    0, -7, -7, -24, 0, -24, 0, -3,
    3, 0, 3, -37, -21, 4, 0, 0,
    -41, 0, 0, 0, -1, -1, -6, 0,
    -8, -8, 0, -8, 0, 0, 0, 0,
    0, 0, 3, 0, 3, 0, 0, -3,
    0, -2, 10, 0, -1, -3, 0, 0,
    2, -3, -3, -7, -5, 0, -13, 0,
    0, 0, -3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 2, 0, 0, 0, 6,
    0, 0, -4, 0, 0, -6, 2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -8, 8, 0, -20,
    -29, -22, 9, -8, 0, -36, 0, 6,
    0, 5, 5, 0, 0, 0, -30, 0,
    -28, -12, 0, -24, -28, -8, -23, -27,
    -27, -27, -22, -3, 5, 0, -6, -20,
    -18, 0, -5, 0, -19, -1, 5, 0,
    0, 0, 0, 0, 0, -20, 0, -16,
    -4, 0, -11, -12, 0, -9, -6, -8,
    -6, -9, 0, 0, 5, -24, 3, 0,
    3, -9, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -6, 0, -9, 0,
    0, -4, -5, -8, -8, -16, 0, -16,
    0, -8, 4, 5, -18, -35, -28, 2,
    -14, 0, -35, -6, 0, 0, 0, 0,
    0, 0, 0, -29, 0, -27, -13, 0,
    -22, -24, -9, -20, -19, -18, -19, -20,
    0, 0, 3, -12, 5, 0, 3, -7,
    0, 0, -3, 0, 0, 0, 0, 0,
    0, 0, -1, 0, -4, 0, 0, 0,
    0, 0, 0, -8, 0, -8, 0, 0,
    -10, 0, -1, 0, 0, -8, 0, 0,
    0, 0, -22, 0, -19, -17, -2, -26,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -3, 0, -3, 0, 0, -13,
    0, -3, -8, 0, -10, 0, 0, 0,
    0, -28, 0, -19, -16, -9, -27, 0,
    -3, 0, 0, -2, 0, 0, 0, -1,
    0, -5, -6, -5, -6, 0, 1, 0,
    5, 6, 0, -3, 0, 0, 0, 0,
    -20, 0, -13, -9, 5, -20, 0, 1,
    0, -1, 3, 0, 0, 0, 6, 0,
    0, 2, 0, 4, 0, 0, 4, 0,
    0, 0, 3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -1, 0, 4, -1,
    0, -6, 0, 0, 0, 0, -18, 0,
    -17, -13, -4, -24, 0, 0, 0, 0,
    0, 0, 0, 3, 0, 0, -1, -2,
    -1, 0, 0, 12, 0, -2, -20, 0,
    12, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -7, 0, -6, 1,
    0, 0, 0, 4, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -7,
    0, 0, 0, 0, -24, 0, -12, -11,
    0, -22, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 11, 0,
    0, 0, 0, 0, 0, 0, 0, -6,
    0, 0, -7, 6, 0, -12, 0, 0,
    0, 0, -24, 0, -16, -14, 0, -22,
    0, -7, 0, -6, 0, 0, 0, -3,
    0, -2, 0, 0, 0, 0, 0, 6,
    0, 2, -28, -12, -7, 0, 0, -30,
    0, 0, 0, -11, 0, -13, -19, 0,
    -11, 0, -8, 0, 0, 0, -2, 7,
    0, 0, 0, 0, 0, 0, -7, 0,
    0, 0, 0, -8, 0, 0, 0, 0,
    -26, 0, -18, -13, -1, -27, 0, 0,
    0, 0, 0, 0, 0, 2, 0, 0,
    -2, 0, -2, 3, 0, -2, 3, -1,
    7, 0, -6, 0, 0, 0, 0, -18,
    0, -12, 0, -1, -17, 0, 0, 0,
    -2, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -23,
    -11, -7, 0, 0, -20, 0, -27, 0,
    -12, -6, -16, -19, 0, -5, 0, -5,
    0, 0, 0, -3, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -5, 2, 0,
    -10, 0, 0, 0, 0, -27, 0, -14,
    -8, 0, -18, 0, -4, 0, -6, 0,
    0, 0, 0, 3, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 3, 0, -8,
    0, 0, 0, 0, -25, 0, -14, -8,
    0, -19, 0, -4, 0, -6, 0, 0,
    0, 1, 0, 0, 0, 0, 0, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 40,
    .right_class_cnt     = 31,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t lv_font_HarmonyOS_Sans_SC_Medium_16 = {
#else
lv_font_t lv_font_HarmonyOS_Sans_SC_Medium_16 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 16,          /*The maximum line height required by the font*/
    .base_line = 2,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if LV_FONT_HARMONYOS_SANS_SC_MEDIUM_16*/

