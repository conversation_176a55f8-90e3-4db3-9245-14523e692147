/*
* Copyright 2025 NXP
* NXP Proprietary. This software is owned or controlled by NXP and may only be used strictly in
* accordance with the applicable license terms. By expressly accepting such terms or by downloading, installing,
* activating and/or otherwise using the software, you are agreeing that you have read, and that you agree to
* comply with and are bound by, such license terms.  If you do not agree to be bound by the applicable license
* terms, then you may not retain, install, activate or otherwise use the software.
*/

#include "lvgl.h"
#include <stdio.h>
#include "gui_guider.h"
#include "events_init.h"
#include "widgets_init.h"
#include "custom.h"



void setup_scr_motor_update_page(lv_ui *ui)
{
    //Write codes motor_update_page
    ui->motor_update_page = lv_obj_create(NULL);
    lv_obj_set_size(ui->motor_update_page, 320, 240);
    lv_obj_set_scrollbar_mode(ui->motor_update_page, LV_SCROLLBAR_MODE_OFF);

    //Write style for motor_update_page, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->motor_update_page, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->motor_update_page, lv_color_hex(0x242424), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->motor_update_page, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes motor_update_page_cont_1
    ui->motor_update_page_cont_1 = lv_obj_create(ui->motor_update_page);
    lv_obj_set_pos(ui->motor_update_page_cont_1, 0, 1);
    lv_obj_set_size(ui->motor_update_page_cont_1, 319, 29);
    lv_obj_set_scrollbar_mode(ui->motor_update_page_cont_1, LV_SCROLLBAR_MODE_OFF);

    //Write style for motor_update_page_cont_1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->motor_update_page_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->motor_update_page_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->motor_update_page_cont_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->motor_update_page_cont_1, lv_color_hex(0x2c2c2c), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->motor_update_page_cont_1, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->motor_update_page_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->motor_update_page_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->motor_update_page_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->motor_update_page_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->motor_update_page_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes motor_update_page_led_state
    ui->motor_update_page_led_state = lv_led_create(ui->motor_update_page_cont_1);
    lv_led_set_brightness(ui->motor_update_page_led_state, 255);
    lv_led_set_color(ui->motor_update_page_led_state, lv_color_hex(0xffffff));
    lv_obj_set_pos(ui->motor_update_page_led_state, 224, 6);
    lv_obj_set_size(ui->motor_update_page_led_state, 14, 14);

    //Write codes motor_update_page_imgbtn_back
    ui->motor_update_page_imgbtn_back = lv_imgbtn_create(ui->motor_update_page_cont_1);
    lv_obj_add_flag(ui->motor_update_page_imgbtn_back, LV_OBJ_FLAG_CHECKABLE);
    lv_imgbtn_set_src(ui->motor_update_page_imgbtn_back, LV_IMGBTN_STATE_RELEASED, NULL, &_back01_ico_alpha_26x26, NULL);
    ui->motor_update_page_imgbtn_back_label = lv_label_create(ui->motor_update_page_imgbtn_back);
    lv_label_set_text(ui->motor_update_page_imgbtn_back_label, "");
    lv_label_set_long_mode(ui->motor_update_page_imgbtn_back_label, LV_LABEL_LONG_WRAP);
    lv_obj_align(ui->motor_update_page_imgbtn_back_label, LV_ALIGN_CENTER, 0, 0);
    lv_obj_set_style_pad_all(ui->motor_update_page_imgbtn_back, 0, LV_STATE_DEFAULT);
    lv_obj_set_pos(ui->motor_update_page_imgbtn_back, 1, -1);
    lv_obj_set_size(ui->motor_update_page_imgbtn_back, 26, 26);

    //Write style for motor_update_page_imgbtn_back, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_text_color(ui->motor_update_page_imgbtn_back, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->motor_update_page_imgbtn_back, &lv_font_montserratMedium_32, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->motor_update_page_imgbtn_back, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->motor_update_page_imgbtn_back, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->motor_update_page_imgbtn_back, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_clip_corner(ui->motor_update_page_imgbtn_back, true, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->motor_update_page_imgbtn_back, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write style for motor_update_page_imgbtn_back, Part: LV_PART_MAIN, State: LV_STATE_PRESSED.
    lv_obj_set_style_img_recolor_opa(ui->motor_update_page_imgbtn_back, 0, LV_PART_MAIN|LV_STATE_PRESSED);
    lv_obj_set_style_img_opa(ui->motor_update_page_imgbtn_back, 255, LV_PART_MAIN|LV_STATE_PRESSED);
    lv_obj_set_style_text_color(ui->motor_update_page_imgbtn_back, lv_color_hex(0xFF33FF), LV_PART_MAIN|LV_STATE_PRESSED);
    lv_obj_set_style_text_font(ui->motor_update_page_imgbtn_back, &lv_font_montserratMedium_10, LV_PART_MAIN|LV_STATE_PRESSED);
    lv_obj_set_style_text_opa(ui->motor_update_page_imgbtn_back, 255, LV_PART_MAIN|LV_STATE_PRESSED);
    lv_obj_set_style_shadow_width(ui->motor_update_page_imgbtn_back, 0, LV_PART_MAIN|LV_STATE_PRESSED);

    //Write style for motor_update_page_imgbtn_back, Part: LV_PART_MAIN, State: LV_STATE_CHECKED.
    lv_obj_set_style_img_recolor_opa(ui->motor_update_page_imgbtn_back, 0, LV_PART_MAIN|LV_STATE_CHECKED);
    lv_obj_set_style_img_opa(ui->motor_update_page_imgbtn_back, 255, LV_PART_MAIN|LV_STATE_CHECKED);
    lv_obj_set_style_text_color(ui->motor_update_page_imgbtn_back, lv_color_hex(0xFF33FF), LV_PART_MAIN|LV_STATE_CHECKED);
    lv_obj_set_style_text_font(ui->motor_update_page_imgbtn_back, &lv_font_montserratMedium_10, LV_PART_MAIN|LV_STATE_CHECKED);
    lv_obj_set_style_text_opa(ui->motor_update_page_imgbtn_back, 255, LV_PART_MAIN|LV_STATE_CHECKED);
    lv_obj_set_style_shadow_width(ui->motor_update_page_imgbtn_back, 0, LV_PART_MAIN|LV_STATE_CHECKED);

    //Write style for motor_update_page_imgbtn_back, Part: LV_PART_MAIN, State: LV_IMGBTN_STATE_RELEASED.
    lv_obj_set_style_img_recolor_opa(ui->motor_update_page_imgbtn_back, 0, LV_PART_MAIN|LV_IMGBTN_STATE_RELEASED);
    lv_obj_set_style_img_opa(ui->motor_update_page_imgbtn_back, 255, LV_PART_MAIN|LV_IMGBTN_STATE_RELEASED);

    //Write codes motor_update_page_label_link_state
    ui->motor_update_page_label_link_state = lv_label_create(ui->motor_update_page_cont_1);
    lv_label_set_text(ui->motor_update_page_label_link_state, "未连接");
    lv_label_set_long_mode(ui->motor_update_page_label_link_state, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->motor_update_page_label_link_state, 243, 6);
    lv_obj_set_size(ui->motor_update_page_label_link_state, 72, 18);

    //Write style for motor_update_page_label_link_state, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->motor_update_page_label_link_state, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->motor_update_page_label_link_state, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->motor_update_page_label_link_state, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->motor_update_page_label_link_state, &lv_font_HarmonyOS_Sans_SC_Medium_14, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->motor_update_page_label_link_state, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->motor_update_page_label_link_state, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->motor_update_page_label_link_state, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->motor_update_page_label_link_state, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->motor_update_page_label_link_state, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->motor_update_page_label_link_state, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->motor_update_page_label_link_state, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->motor_update_page_label_link_state, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->motor_update_page_label_link_state, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->motor_update_page_label_link_state, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes motor_update_page_label_state
    ui->motor_update_page_label_state = lv_label_create(ui->motor_update_page);
    lv_label_set_text(ui->motor_update_page_label_state, "升级完成");
    lv_label_set_long_mode(ui->motor_update_page_label_state, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->motor_update_page_label_state, 4, 149);
    lv_obj_set_size(ui->motor_update_page_label_state, 285, 23);

    //Write style for motor_update_page_label_state, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->motor_update_page_label_state, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->motor_update_page_label_state, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->motor_update_page_label_state, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->motor_update_page_label_state, &lv_font_HarmonyOS_Sans_SC_Medium_16, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->motor_update_page_label_state, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->motor_update_page_label_state, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->motor_update_page_label_state, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->motor_update_page_label_state, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->motor_update_page_label_state, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->motor_update_page_label_state, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->motor_update_page_label_state, 200, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->motor_update_page_label_state, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->motor_update_page_label_state, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->motor_update_page_label_state, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes motor_update_page_btn_mode
    ui->motor_update_page_btn_mode = lv_btn_create(ui->motor_update_page);
    ui->motor_update_page_btn_mode_label = lv_label_create(ui->motor_update_page_btn_mode);
    lv_label_set_text(ui->motor_update_page_btn_mode_label, "切换为升级模式");
    lv_label_set_long_mode(ui->motor_update_page_btn_mode_label, LV_LABEL_LONG_WRAP);
    lv_obj_align(ui->motor_update_page_btn_mode_label, LV_ALIGN_CENTER, 0, 0);
    lv_obj_set_style_pad_all(ui->motor_update_page_btn_mode, 0, LV_STATE_DEFAULT);
    lv_obj_set_width(ui->motor_update_page_btn_mode_label, LV_PCT(100));
    lv_obj_set_pos(ui->motor_update_page_btn_mode, 12, 48);
    lv_obj_set_size(ui->motor_update_page_btn_mode, 143, 51);

    //Write style for motor_update_page_btn_mode, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->motor_update_page_btn_mode, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->motor_update_page_btn_mode, lv_color_hex(0x4e80ee), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->motor_update_page_btn_mode, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui->motor_update_page_btn_mode, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->motor_update_page_btn_mode, 5, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->motor_update_page_btn_mode, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->motor_update_page_btn_mode, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->motor_update_page_btn_mode, &lv_font_HarmonyOS_Sans_SC_Medium_16, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->motor_update_page_btn_mode, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->motor_update_page_btn_mode, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes motor_update_page_btn_erase_flash
    ui->motor_update_page_btn_erase_flash = lv_btn_create(ui->motor_update_page);
    ui->motor_update_page_btn_erase_flash_label = lv_label_create(ui->motor_update_page_btn_erase_flash);
    lv_label_set_text(ui->motor_update_page_btn_erase_flash_label, "擦除Flash");
    lv_label_set_long_mode(ui->motor_update_page_btn_erase_flash_label, LV_LABEL_LONG_WRAP);
    lv_obj_align(ui->motor_update_page_btn_erase_flash_label, LV_ALIGN_CENTER, 0, 0);
    lv_obj_set_style_pad_all(ui->motor_update_page_btn_erase_flash, 0, LV_STATE_DEFAULT);
    lv_obj_set_width(ui->motor_update_page_btn_erase_flash_label, LV_PCT(100));
    lv_obj_set_pos(ui->motor_update_page_btn_erase_flash, 178, 48);
    lv_obj_set_size(ui->motor_update_page_btn_erase_flash, 123, 50);

    //Write style for motor_update_page_btn_erase_flash, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->motor_update_page_btn_erase_flash, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->motor_update_page_btn_erase_flash, lv_color_hex(0x55b685), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->motor_update_page_btn_erase_flash, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui->motor_update_page_btn_erase_flash, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->motor_update_page_btn_erase_flash, 5, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->motor_update_page_btn_erase_flash, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->motor_update_page_btn_erase_flash, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->motor_update_page_btn_erase_flash, &lv_font_HarmonyOS_Sans_SC_Medium_16, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->motor_update_page_btn_erase_flash, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->motor_update_page_btn_erase_flash, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes motor_update_page_btn_start
    ui->motor_update_page_btn_start = lv_btn_create(ui->motor_update_page);
    ui->motor_update_page_btn_start_label = lv_label_create(ui->motor_update_page_btn_start);
    lv_label_set_text(ui->motor_update_page_btn_start_label, "开始升级");
    lv_label_set_long_mode(ui->motor_update_page_btn_start_label, LV_LABEL_LONG_WRAP);
    lv_obj_align(ui->motor_update_page_btn_start_label, LV_ALIGN_CENTER, 0, 0);
    lv_obj_set_style_pad_all(ui->motor_update_page_btn_start, 0, LV_STATE_DEFAULT);
    lv_obj_set_width(ui->motor_update_page_btn_start_label, LV_PCT(100));
    lv_obj_set_pos(ui->motor_update_page_btn_start, 9, 187);
    lv_obj_set_size(ui->motor_update_page_btn_start, 308, 46);

    //Write style for motor_update_page_btn_start, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->motor_update_page_btn_start, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->motor_update_page_btn_start, lv_color_hex(0x4e80ee), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->motor_update_page_btn_start, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui->motor_update_page_btn_start, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->motor_update_page_btn_start, 5, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->motor_update_page_btn_start, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->motor_update_page_btn_start, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->motor_update_page_btn_start, &lv_font_HarmonyOS_Sans_SC_Medium_16, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->motor_update_page_btn_start, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->motor_update_page_btn_start, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes motor_update_page_bar_plan
    ui->motor_update_page_bar_plan = lv_bar_create(ui->motor_update_page);
    lv_obj_set_style_anim_time(ui->motor_update_page_bar_plan, 1000, 0);
    lv_bar_set_mode(ui->motor_update_page_bar_plan, LV_BAR_MODE_NORMAL);
    lv_bar_set_range(ui->motor_update_page_bar_plan, 0, 100);
    lv_bar_set_value(ui->motor_update_page_bar_plan, 50, LV_ANIM_OFF);
    lv_obj_set_pos(ui->motor_update_page_bar_plan, 12, 114);
    lv_obj_set_size(ui->motor_update_page_bar_plan, 290, 16);

    //Write style for motor_update_page_bar_plan, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->motor_update_page_bar_plan, 60, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->motor_update_page_bar_plan, lv_color_hex(0x2195f6), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->motor_update_page_bar_plan, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->motor_update_page_bar_plan, 10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->motor_update_page_bar_plan, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write style for motor_update_page_bar_plan, Part: LV_PART_INDICATOR, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->motor_update_page_bar_plan, 255, LV_PART_INDICATOR|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->motor_update_page_bar_plan, lv_color_hex(0x2195f6), LV_PART_INDICATOR|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->motor_update_page_bar_plan, LV_GRAD_DIR_NONE, LV_PART_INDICATOR|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->motor_update_page_bar_plan, 10, LV_PART_INDICATOR|LV_STATE_DEFAULT);

    //The custom code of motor_update_page.


    //Update current screen layout.
    lv_obj_update_layout(ui->motor_update_page);

}
